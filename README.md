# 🥷 Shinobi Chronicles - Version Godot

Bienvenue dans **Shinobi Chronicles**, un jeu de rôle ninja développé avec Godot Engine, basé sur le concept original développé avec Phaser.

## 🎮 Description du Jeu

Shinobi Chronicles est un RPG d'exploration et de combat tour par tour où vous incarnez un ninja appartenant à l'un des trois clans légendaires. Explorez un monde ouvert, combattez des ennemis, maîtrisez des jutsu puissants et progressez dans votre quête ninja.

## 🏛️ Les Trois Clans

### 🔥 Clan Ryūjin (Feu)
- **Bonus** : +2 Attaque, +20% Résistance au feu
- **Jutsu spécial** : Tranche du Dragon
- **Style** : Combattant offensif maîtrisant les flammes

### 👤 Clan Yūrei (Ombre)
- **Bonus** : +3 Furtivité, +30% Puissance Genjutsu
- **Jutsu spécial** : Clone d'Ombre
- **Style** : Assassin furtif maîtrisant les ombres

### 💨 Clan Kaze (Vent)
- **Bonus** : +2 Vitesse, +25% Puissance du Vent
- **Jutsu spécial** : Lame de <PERSON>ent
- **Style** : Combattant rapide maîtrisant les courants d'air

## 🎯 Systèmes de Jeu

### ⚡ Système de Chakra
- **Régénération automatique** : 1 point par minute
- **Coût de mouvement** : 1 chakra par déplacement
- **Scan de zone** : 5 chakra pour révéler les alentours
- **Jutsu** : Coût variable selon la technique

### ⚔️ Combat Tour par Tour
- **Éléments** : Feu > Vent > Terre > Eau > Feu
- **Ombres** : Élément neutre avec effets spéciaux
- **IA ennemie** : Patterns variés (agressif, tactique, hit-and-run)
- **Effets de statut** : Brûlure, confusion, ralentissement, etc.

### 🗺️ Exploration
- **Fog of War** : Révélation progressive de la carte
- **Régions** : Débloquées selon le niveau
- **Rencontres aléatoires** : Taux variable selon la région
- **Scan** : Révèle une zone étendue autour du joueur

## 🎮 Contrôles

### Mouvement
- **WASD** ou **Flèches directionnelles** : Se déplacer
- **F** : Scanner la zone (coûte 5 chakra)

### Actions
- **E** ou **Espace** : Interagir
- **I** : Inventaire (à implémenter)
- **S** : Compétences (à implémenter)
- **M** : Carte (à implémenter)
- **Échap** : Menu de pause

### Combat
- **Attaquer** : Attaque de base sans coût
- **Jutsu** : Utiliser une technique spéciale
- **Objets** : Utiliser un objet (à implémenter)
- **Fuir** : Tentative de fuite (chance basée sur la vitesse)

## 🌍 Régions du Monde

1. **Village de Départ** (Niv. 1-3)
   - Zone sûre avec peu d'ennemis
   - Idéal pour apprendre les bases

2. **Lisière de la Forêt** (Niv. 2-5)
   - Première zone d'exploration
   - Sangliers sauvages et loups

3. **Bois Sombres** (Niv. 4-8)
   - Zone dangereuse avec ninjas renégats
   - Créatures élémentaires

4. **Sentier de Montagne** (Niv. 6-12)
   - Zone de haut niveau
   - Boss et créatures puissantes

## 👾 Ennemis

### Niveau Débutant (1-3)
- **Sanglier Sauvage** : Ennemi de terre agressif
- **Loup de Forêt** : Rapide avec attaques hit-and-run

### Niveau Intermédiaire (4-8)
- **Ninja de l'Ombre** : Utilise des techniques d'ombre
- **Salamandre de Feu** : Crache des flammes
- **Esprit de l'Eau** : Magie élémentaire

### Niveau Avancé (8-12)
- **Golem de Terre** : Tank avec haute défense
- **Bête de l'Ombre** : Berserker corrompu
- **Maître Élémentaire** : Boss final multi-éléments

## 🎭 Jutsu Disponibles

### Techniques de Base
- **Attaque de Base** : Gratuite, dégâts modérés
- **Rafale de Vent** : Repousse l'ennemi
- **Technique de Substitution** : Évite la prochaine attaque

### Techniques de Clan
- **Tranche du Dragon** (Ryūjin) : Feu + brûlure
- **Clone d'Ombre** (Yūrei) : Ombre + confusion
- **Lame de Vent** (Kaze) : Vent + ralentissement

### Techniques Avancées
- **Tornade de Flammes** : Technique ultime du feu
- **Assassinat de l'Ombre** : Technique ultime de l'ombre
- **Frappe Ouragan** : Technique ultime du vent
- **Fusion Élémentaire** : Technique interdite (Niv. 10)

## 💎 Système de Progression

### Niveaux
- **XP de base** : 100 points
- **Multiplicateur** : 1.5x par niveau
- **Niveau maximum** : 50

### Montée de niveau
- **HP** : +15-25 points
- **Chakra** : +3-8 points
- **Attaque** : +2-5 points
- **Défense** : +1-4 points
- **Vitesse** : +1-3 points

## 💾 Système de Sauvegarde

- **3 slots** de sauvegarde disponibles
- **Sauvegarde automatique** dans le slot 1
- **Compression** et vérification d'intégrité
- **Backup automatique** en cas de corruption

## 🛠️ Installation et Lancement

1. **Prérequis** : Godot Engine 4.4+
2. **Ouvrir le projet** dans Godot
3. **Lancer** la scène Main.tscn
4. **Jouer** !

## 🎯 Fonctionnalités Implémentées

✅ **Système de base**
- Configuration centralisée
- Gestion d'état global
- Système de sauvegarde

✅ **Gameplay**
- Exploration avec fog of war
- Combat tour par tour
- Système de chakra
- Progression et niveaux

✅ **Interface**
- HUD temps réel
- Minimap
- Messages système
- Interface de combat

✅ **Données**
- 10+ ennemis variés
- 18+ jutsu différents
- 25+ objets et équipements

## 🚧 À Implémenter

- [ ] Interface d'inventaire complète
- [ ] Système de quêtes
- [ ] Arbre de compétences
- [ ] Système de craft
- [ ] Audio (musiques et effets)
- [ ] Animations avancées
- [ ] Donjons et boss

## 🎉 Différences avec la Version Phaser

### Améliorations
- **Architecture plus robuste** avec les systèmes Godot
- **Gestion de scènes** native
- **Performance optimisée** pour les jeux 2D
- **Système de signaux** pour la communication

### Nouvelles Fonctionnalités
- **DataLoader** pour charger les données depuis JSON
- **SceneManager** pour la gestion des transitions
- **Système de HUD** modulaire
- **Architecture MVC** plus claire

## 🤝 Contribution

Le projet est ouvert aux contributions ! N'hésitez pas à :
- Signaler des bugs
- Proposer des améliorations
- Ajouter du contenu (ennemis, jutsu, régions)
- Améliorer l'interface

## 📜 Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.

---

**Que l'aventure ninja commence ! 🥷✨**
