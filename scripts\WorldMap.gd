extends Control

# Carte du monde mobile pour Shinobi Chronicles

var tilemap: TileMap
var player_marker: Sprite2D
var camera: Camera2D
var ui_layer: CanvasLayer

# Données
var player_data: Dictionary
var game_progress: Dictionary
var villages = {}
var current_tile_pos: Vector2

# UI Mobile
var top_bar: Panel
var bottom_bar: Panel
var move_buttons: Array = []

# Signaux
signal village_selected(village_id: String)
signal battle_triggered(enemy_data: Dictionary)
signal menu_requested

func _ready():
	print("🗺️ Carte du monde mobile initialisée")
	setup_world_map()

func initialize(player_data: Dictionary, game_progress: Dictionary):
	self.player_data = player_data
	self.game_progress = game_progress
	current_tile_pos = player_data.position
	
	# Mettre à jour la position du joueur
	if player_marker:
		update_player_position()
	
	# Mettre à jour l'UI
	update_ui()

func setup_world_map():
	set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	# Créer la tilemap
	create_tilemap()
	
	# C<PERSON>er le marqueur du joueur
	create_player_marker()
	
	# Créer l'interface mobile
	create_mobile_ui()
	
	# Définir les villages
	setup_villages()

func create_tilemap():
	# Container pour la carte
	var map_container = Node2D.new()
	map_container.name = "MapContainer"
	add_child(map_container)
	
	# TileMap pour le terrain
	tilemap = TileMap.new()
	tilemap.name = "WorldTileMap"
	map_container.add_child(tilemap)
	
	# Créer les tiles de terrain
	create_terrain_tiles()
	
	# Caméra pour la carte
	camera = Camera2D.new()
	camera.name = "WorldCamera"
	camera.enabled = true
	camera.zoom = Vector2(1.5, 1.5)  # Zoom pour mobile
	map_container.add_child(camera)

func create_terrain_tiles():
	# Créer une carte simple 20x20
	var map_size = 20
	
	# Générer le terrain de base
	for x in range(map_size):
		for y in range(map_size):
			var tile_type = get_tile_type(x, y)
			create_visual_tile(Vector2(x, y), tile_type)

func get_tile_type(x: int, y: int) -> String:
	# Logique simple pour les types de terrain
	if x < 5 and y < 5:
		return "village"  # Zone de village
	elif x < 10 and y < 10:
		return "forest"  # Forêt
	elif x > 15 or y > 15:
		return "mountain"  # Montagnes
	else:
		return "plains"  # Plaines

func create_visual_tile(pos: Vector2, tile_type: String):
	# Créer une représentation visuelle de la tile
	var tile_sprite = Sprite2D.new()
	tile_sprite.position = pos * 64  # Taille de tile 64x64
	
	# Créer la texture selon le type
	var image = Image.create(64, 64, false, Image.FORMAT_RGB8)
	var color = get_tile_color(tile_type)
	image.fill(color)
	
	# Ajouter des détails
	add_tile_details(image, tile_type)
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	tile_sprite.texture = texture
	
	tilemap.add_child(tile_sprite)

func get_tile_color(tile_type: String) -> Color:
	match tile_type:
		"village":
			return Color(0.8, 0.6, 0.4)  # Beige
		"forest":
			return Color(0.2, 0.6, 0.2)  # Vert foncé
		"plains":
			return Color(0.4, 0.8, 0.3)  # Vert clair
		"mountain":
			return Color(0.5, 0.5, 0.5)  # Gris
		_:
			return Color.WHITE

func add_tile_details(image: Image, tile_type: String):
	# Ajouter des détails visuels selon le type
	match tile_type:
		"village":
			# Ajouter des "bâtiments"
			for i in range(3):
				var x = 10 + i * 15
				var y = 20 + i * 10
				for dx in range(8):
					for dy in range(8):
						if x + dx < 64 and y + dy < 64:
							image.set_pixel(x + dx, y + dy, Color.BROWN)
		
		"forest":
			# Ajouter des "arbres"
			for i in range(5):
				var x = 10 + i * 10
				var y = 15 + (i % 2) * 20
				for dx in range(6):
					for dy in range(6):
						if x + dx < 64 and y + dy < 64:
							image.set_pixel(x + dx, y + dy, Color.DARK_GREEN)
		
		"mountain":
			# Ajouter des "pics"
			for i in range(3):
				var x = 15 + i * 15
				for y in range(20, 50):
					if x < 64 and y < 64:
						image.set_pixel(x, y, Color.DARK_GRAY)

func create_player_marker():
	# Marqueur du joueur sur la carte
	player_marker = Sprite2D.new()
	player_marker.name = "PlayerMarker"
	
	# Créer une texture de ninja
	var image = Image.create(32, 32, false, Image.FORMAT_RGB8)
	image.fill(Color.TRANSPARENT)
	
	# Dessiner un ninja simple
	for x in range(8, 24):
		for y in range(8, 24):
			image.set_pixel(x, y, Color.RED)
	
	# Bordure
	for i in range(32):
		image.set_pixel(i, 0, Color.BLACK)
		image.set_pixel(i, 31, Color.BLACK)
		image.set_pixel(0, i, Color.BLACK)
		image.set_pixel(31, i, Color.BLACK)
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	player_marker.texture = texture
	
	tilemap.add_child(player_marker)
	update_player_position()

func create_mobile_ui():
	# Layer UI au-dessus de tout
	ui_layer = CanvasLayer.new()
	ui_layer.name = "MobileUI"
	ui_layer.layer = 100
	add_child(ui_layer)
	
	# Barre du haut
	create_top_bar()
	
	# Barre du bas avec contrôles
	create_bottom_bar()

func create_top_bar():
	top_bar = Panel.new()
	top_bar.set_anchors_and_offsets_preset(Control.PRESET_TOP_WIDE)
	top_bar.size.y = 80
	top_bar.modulate = Color(0, 0, 0, 0.8)
	ui_layer.add_child(top_bar)
	
	var hbox = HBoxContainer.new()
	hbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	top_bar.add_child(hbox)
	
	# Bouton menu
	var menu_btn = Button.new()
	menu_btn.text = "☰"
	menu_btn.custom_minimum_size = Vector2(60, 60)
	menu_btn.add_theme_font_size_override("font_size", 24)
	menu_btn.pressed.connect(_on_menu_pressed)
	hbox.add_child(menu_btn)
	
	# Infos joueur
	var player_info = VBoxContainer.new()
	player_info.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	hbox.add_child(player_info)
	
	var name_label = Label.new()
	name_label.text = "Ninja Niv.1"
	name_label.add_theme_font_size_override("font_size", 18)
	name_label.add_theme_color_override("font_color", Color.WHITE)
	player_info.add_child(name_label)
	
	var hp_label = Label.new()
	hp_label.text = "HP: 100/100"
	hp_label.add_theme_font_size_override("font_size", 14)
	hp_label.add_theme_color_override("font_color", Color.GREEN)
	player_info.add_child(hp_label)
	
	# Bouton inventaire
	var inv_btn = Button.new()
	inv_btn.text = "🎒"
	inv_btn.custom_minimum_size = Vector2(60, 60)
	inv_btn.add_theme_font_size_override("font_size", 24)
	inv_btn.pressed.connect(_on_inventory_pressed)
	hbox.add_child(inv_btn)

func create_bottom_bar():
	bottom_bar = Panel.new()
	bottom_bar.set_anchors_and_offsets_preset(Control.PRESET_BOTTOM_WIDE)
	bottom_bar.size.y = 120
	bottom_bar.modulate = Color(0, 0, 0, 0.8)
	ui_layer.add_child(bottom_bar)
	
	# Contrôles de mouvement
	var controls_grid = GridContainer.new()
	controls_grid.columns = 3
	controls_grid.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	controls_grid.size = Vector2(200, 120)
	controls_grid.add_theme_constant_override("h_separation", 10)
	controls_grid.add_theme_constant_override("v_separation", 10)
	bottom_bar.add_child(controls_grid)
	
	# Boutons directionnels
	var directions = [
		{"text": "", "dir": Vector2.ZERO},      # Vide
		{"text": "⬆️", "dir": Vector2(0, -1)},   # Haut
		{"text": "", "dir": Vector2.ZERO},      # Vide
		{"text": "⬅️", "dir": Vector2(-1, 0)},   # Gauche
		{"text": "🎯", "dir": Vector2.ZERO},     # Centre (action)
		{"text": "➡️", "dir": Vector2(1, 0)},    # Droite
		{"text": "", "dir": Vector2.ZERO},      # Vide
		{"text": "⬇️", "dir": Vector2(0, 1)},    # Bas
		{"text": "", "dir": Vector2.ZERO}       # Vide
	]
	
	for i in range(9):
		var btn = Button.new()
		btn.text = directions[i].text
		btn.custom_minimum_size = Vector2(60, 35)
		btn.add_theme_font_size_override("font_size", 20)
		
		if directions[i].dir != Vector2.ZERO:
			btn.pressed.connect(_on_move_pressed.bind(directions[i].dir))
		elif i == 4:  # Bouton central
			btn.pressed.connect(_on_action_pressed)
		else:
			btn.disabled = true
			btn.modulate.a = 0.3
		
		controls_grid.add_child(btn)
		move_buttons.append(btn)

func setup_villages():
	# Définir les villages sur la carte
	villages = {
		"konoha": {"name": "Village de Konoha", "pos": Vector2(2, 2), "unlocked": true},
		"suna": {"name": "Village de Suna", "pos": Vector2(15, 8), "unlocked": false},
		"kiri": {"name": "Village de Kiri", "pos": Vector2(8, 15), "unlocked": false}
	}
	
	# Créer les marqueurs de villages
	for village_id in villages.keys():
		create_village_marker(village_id, villages[village_id])

func create_village_marker(village_id: String, village_data: Dictionary):
	var marker = Sprite2D.new()
	marker.position = village_data.pos * 64
	marker.name = "Village_" + village_id
	
	# Texture de village
	var image = Image.create(48, 48, false, Image.FORMAT_RGB8)
	var color = Color.ORANGE if village_data.unlocked else Color.GRAY
	image.fill(color)
	
	# Ajouter un symbole de village
	for x in range(16, 32):
		for y in range(16, 32):
			image.set_pixel(x, y, Color.BROWN)
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	marker.texture = texture
	
	tilemap.add_child(marker)

func update_player_position():
	if player_marker:
		player_marker.position = current_tile_pos * 64
		
		# Centrer la caméra sur le joueur
		if camera:
			camera.position = player_marker.position

func update_ui():
	if not player_data:
		return
	
	# Mettre à jour les infos dans la barre du haut
	if top_bar and top_bar.get_child_count() > 0:
		var hbox = top_bar.get_child(0)
		if hbox.get_child_count() > 1:
			var player_info = hbox.get_child(1)
			if player_info.get_child_count() > 0:
				player_info.get_child(0).text = player_data.name + " Niv." + str(player_data.level)
				player_info.get_child(1).text = "HP: " + str(player_data.hp) + "/" + str(player_data.max_hp)

# Callbacks
func _on_move_pressed(direction: Vector2):
	var new_pos = current_tile_pos + direction
	
	# Vérifier les limites
	if new_pos.x < 0 or new_pos.x >= 20 or new_pos.y < 0 or new_pos.y >= 20:
		print("🚫 Limite de la carte")
		return
	
	# Déplacer le joueur
	current_tile_pos = new_pos
	player_data.position = new_pos
	update_player_position()
	
	# Chance de combat aléatoire
	if randf() < 0.15:
		trigger_random_battle()
	
	print("🚶 Déplacement vers: ", current_tile_pos)

func _on_action_pressed():
	# Vérifier s'il y a un village à cette position
	for village_id in villages.keys():
		var village = villages[village_id]
		if village.pos == current_tile_pos and village.unlocked:
			print("🏘️ Entrée dans le village: ", village.name)
			emit_signal("village_selected", village_id)
			return
	
	# Sinon, action de fouille
	print("🔍 Fouille de la zone...")
	if randf() < 0.3:
		print("🎁 Objet trouvé!")
		# Ajouter un objet aléatoire

func _on_menu_pressed():
	emit_signal("menu_requested")

func _on_inventory_pressed():
	print("🎒 Ouverture de l'inventaire")
	# Implémenter plus tard

func trigger_random_battle():
	var enemies = [
		{"name": "Bandit", "level": 1, "hp": 30},
		{"name": "Loup Sauvage", "level": 2, "hp": 40},
		{"name": "Ninja Renégat", "level": 3, "hp": 50}
	]
	
	var enemy = enemies[randi() % enemies.size()]
	print("⚔️ Combat contre: ", enemy.name)
	emit_signal("battle_triggered", enemy)
