extends Control

# Scène de combat pour Shinobi Chronicles
class_name BattleScene

@onready var player_info: VBoxContainer
@onready var enemy_info: VBoxContainer
@onready var action_buttons: HBoxContainer
@onready var combat_log: RichTextLabel
@onready var jutsu_panel: Panel

var game_state: GameState
var combat_system: CombatSystem
var current_enemy: Dictionary

# UI Elements
var player_hp_bar: ProgressBar
var player_chakra_bar: ProgressBar
var enemy_hp_bar: ProgressBar
var player_name_label: Label
var enemy_name_label: Label

# Action buttons
var attack_button: Button
var jutsu_button: Button
var item_button: Button
var flee_button: Button

signal battle_finished(victory: bool, rewards: Dictionary)

func _ready():
	setup_ui()
	setup_buttons()

func setup_ui():
	# Configuration de base de l'interface
	set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	# Créer le layout principal
	var main_vbox = VBoxContainer.new()
	main_vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(main_vbox)
	
	# Zone d'information des combattants
	var info_hbox = HBoxContainer.new()
	info_hbox.size_flags_vertical = Control.SIZE_EXPAND_FILL
	main_vbox.add_child(info_hbox)
	
	# Info joueur
	player_info = VBoxContainer.new()
	player_info.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	info_hbox.add_child(player_info)
	
	player_name_label = Label.new()
	player_name_label.text = "Joueur"
	player_name_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	player_info.add_child(player_name_label)
	
	player_hp_bar = ProgressBar.new()
	player_hp_bar.show_percentage = false
	player_info.add_child(player_hp_bar)
	
	var hp_label = Label.new()
	hp_label.text = "HP"
	hp_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	player_info.add_child(hp_label)
	
	player_chakra_bar = ProgressBar.new()
	player_chakra_bar.show_percentage = false
	player_chakra_bar.modulate = Color.CYAN
	player_info.add_child(player_chakra_bar)
	
	var chakra_label = Label.new()
	chakra_label.text = "Chakra"
	chakra_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	player_info.add_child(chakra_label)
	
	# Séparateur
	var separator = VSeparator.new()
	info_hbox.add_child(separator)
	
	# Info ennemi
	enemy_info = VBoxContainer.new()
	enemy_info.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	info_hbox.add_child(enemy_info)
	
	enemy_name_label = Label.new()
	enemy_name_label.text = "Ennemi"
	enemy_name_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	enemy_info.add_child(enemy_name_label)
	
	enemy_hp_bar = ProgressBar.new()
	enemy_hp_bar.show_percentage = false
	enemy_hp_bar.modulate = Color.RED
	enemy_info.add_child(enemy_hp_bar)
	
	var enemy_hp_label = Label.new()
	enemy_hp_label.text = "HP"
	enemy_hp_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	enemy_info.add_child(enemy_hp_label)
	
	# Log de combat
	combat_log = RichTextLabel.new()
	combat_log.custom_minimum_size = Vector2(0, 150)
	combat_log.bbcode_enabled = true
	combat_log.scroll_following = true
	main_vbox.add_child(combat_log)
	
	# Boutons d'action
	action_buttons = HBoxContainer.new()
	action_buttons.alignment = BoxContainer.ALIGNMENT_CENTER
	main_vbox.add_child(action_buttons)

func setup_buttons():
	# Bouton d'attaque
	attack_button = Button.new()
	attack_button.text = "Attaquer"
	attack_button.pressed.connect(_on_attack_pressed)
	action_buttons.add_child(attack_button)
	
	# Bouton jutsu
	jutsu_button = Button.new()
	jutsu_button.text = "Jutsu"
	jutsu_button.pressed.connect(_on_jutsu_pressed)
	action_buttons.add_child(jutsu_button)
	
	# Bouton objets
	item_button = Button.new()
	item_button.text = "Objets"
	item_button.pressed.connect(_on_item_pressed)
	action_buttons.add_child(item_button)
	
	# Bouton fuir
	flee_button = Button.new()
	flee_button.text = "Fuir"
	flee_button.pressed.connect(_on_flee_pressed)
	action_buttons.add_child(flee_button)

func initialize_battle(enemy_data: Dictionary, player_state: GameState, combat_sys: CombatSystem):
	game_state = player_state
	combat_system = combat_sys
	current_enemy = enemy_data
	
	# Connecter les signaux du système de combat
	combat_system.combat_ended.connect(_on_combat_ended)
	combat_system.damage_dealt.connect(_on_damage_dealt)
	combat_system.jutsu_used.connect(_on_jutsu_used)
	combat_system.turn_started.connect(_on_turn_started)
	
	# Mettre à jour l'interface
	update_ui()
	
	# Ajouter un message de début
	add_combat_log("[color=yellow]Combat contre " + enemy_data.name + " ![/color]")

func update_ui():
	# Mettre à jour les informations du joueur
	player_name_label.text = game_state.player_data.name + " (Niv. " + str(game_state.player_data.level) + ")"
	player_hp_bar.max_value = game_state.player_data.max_hp
	player_hp_bar.value = game_state.player_data.hp
	player_chakra_bar.max_value = game_state.player_data.max_chakra
	player_chakra_bar.value = game_state.player_data.chakra
	
	# Mettre à jour les informations de l'ennemi
	enemy_name_label.text = current_enemy.name + " (Niv. " + str(current_enemy.level) + ")"
	enemy_hp_bar.max_value = current_enemy.max_hp
	enemy_hp_bar.value = current_enemy.hp

func add_combat_log(message: String):
	combat_log.append_text(message + "\n")

func set_buttons_enabled(enabled: bool):
	attack_button.disabled = not enabled
	jutsu_button.disabled = not enabled
	item_button.disabled = not enabled
	flee_button.disabled = not enabled

# Actions du joueur
func _on_attack_pressed():
	set_buttons_enabled(false)
	combat_system.player_basic_attack(game_state)

func _on_jutsu_pressed():
	show_jutsu_selection()

func _on_item_pressed():
	show_item_selection()

func _on_flee_pressed():
	# Tentative de fuite
	var flee_chance = 0.5  # 50% de chance de base
	
	# Bonus de fuite selon la vitesse
	var speed_bonus = game_state.player_data.speed * 0.05
	flee_chance += speed_bonus
	
	if randf() < flee_chance:
		add_combat_log("[color=green]Vous avez réussi à fuir![/color]")
		emit_signal("battle_finished", false, {})
	else:
		add_combat_log("[color=red]Impossible de fuir![/color]")
		set_buttons_enabled(false)
		# L'ennemi attaque
		await get_tree().create_timer(1.0).timeout
		combat_system.enemy_turn(game_state)

func show_jutsu_selection():
	# Créer un panneau de sélection de jutsu
	var jutsu_list = combat_system.get_available_jutsu(game_state)
	
	if jutsu_list.size() <= 1:  # Seulement l'attaque de base
		add_combat_log("[color=red]Aucun jutsu disponible![/color]")
		return
	
	# Créer une popup avec les jutsu
	var popup = AcceptDialog.new()
	popup.title = "Choisir un Jutsu"
	popup.size = Vector2(300, 200)
	
	var vbox = VBoxContainer.new()
	popup.add_child(vbox)
	
	for jutsu in jutsu_list:
		if jutsu.id == "basic_attack":
			continue
		
		var button = Button.new()
		var jutsu_data = jutsu.data
		button.text = jutsu_data.name + " (" + str(jutsu_data.chakra_cost) + " chakra)"
		
		if not jutsu.can_use:
			button.disabled = true
			button.text += " [Pas assez de chakra]"
		
		button.pressed.connect(_on_jutsu_selected.bind(jutsu.id))
		button.pressed.connect(popup.queue_free)
		vbox.add_child(button)
	
	add_child(popup)
	popup.popup_centered()

func _on_jutsu_selected(jutsu_id: String):
	set_buttons_enabled(false)
	combat_system.player_use_jutsu(jutsu_id, game_state)

func show_item_selection():
	add_combat_log("[color=yellow]Système d'objets à implémenter[/color]")

# Callbacks des signaux
func _on_combat_ended(victory: bool, rewards: Dictionary):
	if victory:
		add_combat_log("[color=green]Victoire![/color]")
		if rewards.has("xp"):
			add_combat_log("XP gagné: " + str(rewards.xp))
		if rewards.has("ryo"):
			add_combat_log("Ryo gagné: " + str(rewards.ryo))
		if rewards.has("loot") and rewards.loot.size() > 0:
			add_combat_log("Objets trouvés!")
	else:
		add_combat_log("[color=red]Défaite...[/color]")
	
	# Attendre un peu avant de fermer
	await get_tree().create_timer(2.0).timeout
	emit_signal("battle_finished", victory, rewards)

func _on_damage_dealt(attacker: Dictionary, target: Dictionary, damage: int, element: String):
	var attacker_name = attacker.get("name", "Joueur")
	var target_name = target.get("name", "Ennemi")
	
	var color = "white"
	if element != "neutral":
		var effectiveness = GameConfig.get_element_effectiveness(element, target.get("element", "neutral"))
		if effectiveness > 1.0:
			color = "green"
		elif effectiveness < 1.0:
			color = "orange"
	
	add_combat_log("[color=" + color + "]" + attacker_name + " inflige " + str(damage) + " dégâts à " + target_name + "[/color]")
	
	# Mettre à jour l'interface
	update_ui()

func _on_jutsu_used(caster: String, jutsu_data: Dictionary):
	var caster_name = "Joueur" if caster == "player" else current_enemy.name
	add_combat_log("[color=cyan]" + caster_name + " utilise " + jutsu_data.name + "![/color]")

func _on_turn_started(is_player_turn: bool):
	if is_player_turn:
		add_combat_log("[color=yellow]Votre tour![/color]")
		set_buttons_enabled(true)
	else:
		add_combat_log("[color=yellow]Tour de l'ennemi...[/color]")
		set_buttons_enabled(false)
