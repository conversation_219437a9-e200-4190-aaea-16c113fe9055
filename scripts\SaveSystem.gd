extends Node

# Système de sauvegarde pour Shinobi Chronicles
class_name SaveSystem

const SAVE_FILE_PATH = "user://shinobi_save_%d.dat"
const BACKUP_FILE_PATH = "user://shinobi_save_%d_backup.dat"

signal save_completed(slot_number)
signal load_completed(slot_number)
signal save_failed(error_message)
signal load_failed(error_message)

# Sauvegarder le jeu dans un slot
func save_game(slot_number: int, game_state: GameState) -> bool:
	if slot_number < 1 or slot_number > GameConfig.SAVE_SLOTS:
		emit_signal("save_failed", "Numéro de slot invalide")
		return false
	
	var save_data = game_state.get_save_data()
	
	# Ajouter des métadonnées
	save_data["save_timestamp"] = Time.get_unix_time_from_system()
	save_data["save_slot"] = slot_number
	save_data["checksum"] = calculate_checksum(save_data)
	
	var file_path = SAVE_FILE_PATH % slot_number
	var backup_path = BACKUP_FILE_PATH % slot_number
	
	# Créer une sauvegarde de l'ancien fichier s'il existe
	if FileAccess.file_exists(file_path):
		var dir = DirAccess.open("user://")
		dir.copy(file_path, backup_path)
	
	# Sauvegarder
	var file = FileAccess.open(file_path, FileAccess.WRITE)
	if file == null:
		emit_signal("save_failed", "Impossible d'ouvrir le fichier de sauvegarde")
		return false
	
	# Compresser et encoder les données
	var json_string = JSON.stringify(save_data)
	var compressed_data = json_string.to_utf8_buffer().compress(FileAccess.COMPRESSION_GZIP)
	
	file.store_32(compressed_data.size())
	file.store_buffer(compressed_data)
	file.close()
	
	emit_signal("save_completed", slot_number)
	return true

# Charger le jeu depuis un slot
func load_game(slot_number: int, game_state: GameState) -> bool:
	if slot_number < 1 or slot_number > GameConfig.SAVE_SLOTS:
		emit_signal("load_failed", "Numéro de slot invalide")
		return false
	
	var file_path = SAVE_FILE_PATH % slot_number
	
	if not FileAccess.file_exists(file_path):
		emit_signal("load_failed", "Fichier de sauvegarde introuvable")
		return false
	
	var file = FileAccess.open(file_path, FileAccess.READ)
	if file == null:
		emit_signal("load_failed", "Impossible d'ouvrir le fichier de sauvegarde")
		return false
	
	# Lire et décompresser les données
	var compressed_size = file.get_32()
	var compressed_data = file.get_buffer(compressed_size)
	file.close()
	
	var decompressed_data = compressed_data.decompress(compressed_size * 10, FileAccess.COMPRESSION_GZIP)
	if decompressed_data.is_empty():
		emit_signal("load_failed", "Erreur de décompression")
		return false
	
	var json_string = decompressed_data.get_string_from_utf8()
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	
	if parse_result != OK:
		emit_signal("load_failed", "Données corrompues")
		return false
	
	var save_data = json.data
	
	# Vérifier l'intégrité
	if not verify_save_integrity(save_data):
		# Essayer de charger la sauvegarde de backup
		if load_backup(slot_number, game_state):
			return true
		else:
			emit_signal("load_failed", "Données corrompues et backup invalide")
			return false
	
	# Charger les données dans le GameState
	game_state.load_save_data(save_data)
	
	emit_signal("load_completed", slot_number)
	return true

# Charger la sauvegarde de backup
func load_backup(slot_number: int, game_state: GameState) -> bool:
	var backup_path = BACKUP_FILE_PATH % slot_number
	
	if not FileAccess.file_exists(backup_path):
		return false
	
	var file = FileAccess.open(backup_path, FileAccess.READ)
	if file == null:
		return false
	
	var compressed_size = file.get_32()
	var compressed_data = file.get_buffer(compressed_size)
	file.close()
	
	var decompressed_data = compressed_data.decompress(compressed_size * 10, FileAccess.COMPRESSION_GZIP)
	if decompressed_data.is_empty():
		return false
	
	var json_string = decompressed_data.get_string_from_utf8()
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	
	if parse_result != OK:
		return false
	
	var save_data = json.data
	
	if not verify_save_integrity(save_data):
		return false
	
	game_state.load_save_data(save_data)
	return true

# Vérifier l'intégrité de la sauvegarde
func verify_save_integrity(save_data: Dictionary) -> bool:
	if not save_data.has("checksum"):
		return false
	
	var stored_checksum = save_data.checksum
	save_data.erase("checksum")
	
	var calculated_checksum = calculate_checksum(save_data)
	save_data["checksum"] = stored_checksum
	
	return stored_checksum == calculated_checksum

# Calculer un checksum simple pour vérifier l'intégrité
func calculate_checksum(data: Dictionary) -> String:
	var json_string = JSON.stringify(data)
	return json_string.md5_text()

# Obtenir les informations d'un slot de sauvegarde
func get_save_info(slot_number: int) -> Dictionary:
	if slot_number < 1 or slot_number > GameConfig.SAVE_SLOTS:
		return {}
	
	var file_path = SAVE_FILE_PATH % slot_number
	
	if not FileAccess.file_exists(file_path):
		return {"exists": false}
	
	var file = FileAccess.open(file_path, FileAccess.READ)
	if file == null:
		return {"exists": false}
	
	var compressed_size = file.get_32()
	var compressed_data = file.get_buffer(compressed_size)
	file.close()
	
	var decompressed_data = compressed_data.decompress(compressed_size * 10, FileAccess.COMPRESSION_GZIP)
	if decompressed_data.is_empty():
		return {"exists": false}
	
	var json_string = decompressed_data.get_string_from_utf8()
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	
	if parse_result != OK:
		return {"exists": false}
	
	var save_data = json.data
	
	return {
		"exists": true,
		"player_name": save_data.get("player_data", {}).get("name", "Ninja"),
		"level": save_data.get("player_data", {}).get("level", 1),
		"clan": save_data.get("player_data", {}).get("clan", "ryujin"),
		"play_time": save_data.get("game_stats", {}).get("play_time", 0.0),
		"timestamp": save_data.get("save_timestamp", 0),
		"version": save_data.get("version", "Unknown")
	}

# Supprimer une sauvegarde
func delete_save(slot_number: int) -> bool:
	if slot_number < 1 or slot_number > GameConfig.SAVE_SLOTS:
		return false
	
	var file_path = SAVE_FILE_PATH % slot_number
	var backup_path = BACKUP_FILE_PATH % slot_number
	
	var dir = DirAccess.open("user://")
	if dir == null:
		return false
	
	# Supprimer le fichier principal et le backup
	if FileAccess.file_exists(file_path):
		dir.remove(file_path)
	
	if FileAccess.file_exists(backup_path):
		dir.remove(backup_path)
	
	return true

# Exporter une sauvegarde
func export_save(slot_number: int, export_path: String) -> bool:
	if slot_number < 1 or slot_number > GameConfig.SAVE_SLOTS:
		return false
	
	var file_path = SAVE_FILE_PATH % slot_number
	
	if not FileAccess.file_exists(file_path):
		return false
	
	var dir = DirAccess.open("user://")
	if dir == null:
		return false
	
	return dir.copy(file_path, export_path) == OK

# Importer une sauvegarde
func import_save(import_path: String, slot_number: int) -> bool:
	if slot_number < 1 or slot_number > GameConfig.SAVE_SLOTS:
		return false
	
	if not FileAccess.file_exists(import_path):
		return false
	
	var file_path = SAVE_FILE_PATH % slot_number
	var dir = DirAccess.open("user://")
	if dir == null:
		return false
	
	return dir.copy(import_path, file_path) == OK

# Obtenir la liste de tous les slots de sauvegarde
func get_all_save_slots() -> Array:
	var slots = []
	
	for i in range(1, GameConfig.SAVE_SLOTS + 1):
		slots.append(get_save_info(i))
	
	return slots
