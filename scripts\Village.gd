extends Control

# Système de village mobile pour Shinobi Chronicles

var village_id: String
var player_data: Dictionary

# UI Elements
var background: TextureRect
var title_label: Label
var buildings_container: GridContainer
var bottom_ui: Panel

# Bâtiments disponibles
var buildings = {
	"dojo": {"name": "🥋 Dojo", "desc": "Entraînement et techniques"},
	"shop": {"name": "🏪 Boutique", "desc": "Acheter des objets"},
	"inn": {"name": "🏨 Auberge", "desc": "Se reposer et sauvegarder"},
	"mission": {"name": "📜 Bureau des Missions", "desc": "Quêtes disponibles"},
	"blacksmith": {"name": "⚒️ Forgeron", "desc": "Améliorer l'équipement"}
}

# Signaux
signal leave_village
signal shop_entered
signal training_started
signal mission_accepted(mission_id: String)

func _ready():
	print("🏘️ Village initialisé")
	setup_village_ui()

func initialize(p_data: Dictionary, v_id: String):
	self.player_data = p_data
	self.village_id = v_id

	# Mettre à jour le titre
	update_village_info()

func setup_village_ui():
	set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	# Arrière-plan du village
	create_village_background()
	
	# Titre du village
	create_village_title()
	
	# Bâtiments
	create_buildings_grid()
	
	# Interface du bas
	create_bottom_interface()

func create_village_background():
	background = TextureRect.new()
	background.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	# Créer un arrière-plan de village
	var image = Image.create(720, 1280, false, Image.FORMAT_RGB8)
	
	# Ciel
	for x in range(720):
		for y in range(400):
			var sky_color = Color(0.6, 0.8, 1.0).lerp(Color(0.8, 0.9, 1.0), float(y) / 400.0)
			image.set_pixel(x, y, sky_color)
	
	# Sol
	for x in range(720):
		for y in range(400, 1280):
			var ground_color = Color(0.4, 0.6, 0.3)
			image.set_pixel(x, y, ground_color)
	
	# Ajouter quelques bâtiments en arrière-plan
	add_background_buildings(image)
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	background.texture = texture
	
	add_child(background)

func add_background_buildings(image: Image):
	# Ajouter des silhouettes de bâtiments
	var building_positions = [
		{"x": 50, "y": 300, "w": 80, "h": 100},
		{"x": 200, "y": 250, "w": 120, "h": 150},
		{"x": 400, "y": 280, "w": 100, "h": 120},
		{"x": 580, "y": 260, "w": 90, "h": 140}
	]
	
	for building in building_positions:
		for x in range(building.x, building.x + building.w):
			for y in range(building.y, building.y + building.h):
				if x < 720 and y < 1280:
					image.set_pixel(x, y, Color(0.3, 0.2, 0.1))

func create_village_title():
	title_label = Label.new()
	title_label.text = "🏘️ VILLAGE"
	title_label.set_anchors_and_offsets_preset(Control.PRESET_CENTER_TOP)
	title_label.position.y = 50
	title_label.size = Vector2(400, 60)
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 28)
	title_label.add_theme_color_override("font_color", Color.GOLD)
	title_label.add_theme_color_override("font_shadow_color", Color.BLACK)
	title_label.add_theme_constant_override("shadow_offset_x", 2)
	title_label.add_theme_constant_override("shadow_offset_y", 2)
	
	add_child(title_label)

func create_buildings_grid():
	# Container pour les bâtiments
	var scroll = ScrollContainer.new()
	scroll.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	scroll.size = Vector2(600, 800)
	scroll.position.y = -200
	add_child(scroll)
	
	buildings_container = GridContainer.new()
	buildings_container.columns = 2
	buildings_container.add_theme_constant_override("h_separation", 20)
	buildings_container.add_theme_constant_override("v_separation", 20)
	scroll.add_child(buildings_container)
	
	# Créer les boutons de bâtiments
	for building_id in buildings.keys():
		create_building_button(building_id, buildings[building_id])

func create_building_button(building_id: String, building_data: Dictionary):
	var button = Button.new()
	button.custom_minimum_size = Vector2(280, 120)
	
	# Container pour le contenu du bouton
	var vbox = VBoxContainer.new()
	
	var name_label = Label.new()
	name_label.text = building_data.name
	name_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	name_label.add_theme_font_size_override("font_size", 20)
	name_label.add_theme_color_override("font_color", Color.WHITE)
	vbox.add_child(name_label)
	
	var desc_label = Label.new()
	desc_label.text = building_data.desc
	desc_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	desc_label.add_theme_font_size_override("font_size", 14)
	desc_label.add_theme_color_override("font_color", Color.LIGHT_GRAY)
	desc_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	vbox.add_child(desc_label)
	
	# Style du bouton
	button.add_theme_font_size_override("font_size", 16)
	button.pressed.connect(_on_building_selected.bind(building_id))
	
	# Ajouter un effet hover
	button.mouse_entered.connect(_on_building_hover.bind(button))
	button.mouse_exited.connect(_on_building_unhover.bind(button))
	
	buildings_container.add_child(button)

func create_bottom_interface():
	bottom_ui = Panel.new()
	bottom_ui.set_anchors_and_offsets_preset(Control.PRESET_BOTTOM_WIDE)
	bottom_ui.size.y = 100
	bottom_ui.modulate = Color(0, 0, 0, 0.8)
	add_child(bottom_ui)
	
	var hbox = HBoxContainer.new()
	hbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	hbox.alignment = BoxContainer.ALIGNMENT_CENTER
	bottom_ui.add_child(hbox)
	
	# Bouton retour
	var back_btn = Button.new()
	back_btn.text = "🔙 Quitter le Village"
	back_btn.custom_minimum_size = Vector2(200, 60)
	back_btn.add_theme_font_size_override("font_size", 18)
	back_btn.pressed.connect(_on_leave_village)
	hbox.add_child(back_btn)
	
	# Spacer
	var spacer = Control.new()
	spacer.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	hbox.add_child(spacer)
	
	# Infos joueur
	var player_info = VBoxContainer.new()
	
	var name_label = Label.new()
	name_label.text = "Ninja Niv.1"
	name_label.add_theme_font_size_override("font_size", 16)
	name_label.add_theme_color_override("font_color", Color.WHITE)
	player_info.add_child(name_label)
	
	var money_label = Label.new()
	money_label.text = "💰 1000 Ryō"
	money_label.add_theme_font_size_override("font_size", 14)
	money_label.add_theme_color_override("font_color", Color.YELLOW)
	player_info.add_child(money_label)
	
	hbox.add_child(player_info)

func update_village_info():
	if not village_id:
		return
	
	var village_names = {
		"konoha": "🍃 Village de Konoha",
		"suna": "🏜️ Village de Suna",
		"kiri": "🌊 Village de Kiri"
	}
	
	if title_label:
		title_label.text = village_names.get(village_id, "🏘️ Village")
	
	# Mettre à jour les infos joueur
	if bottom_ui and player_data:
		update_player_info()

func update_player_info():
	if not bottom_ui or not player_data:
		return
	
	var hbox = bottom_ui.get_child(0)
	if hbox.get_child_count() > 2:
		var player_info = hbox.get_child(2)
		if player_info.get_child_count() > 0:
			player_info.get_child(0).text = player_data.name + " Niv." + str(player_data.level)

# Callbacks
func _on_building_selected(building_id: String):
	print("🏢 Bâtiment sélectionné: ", building_id)
	
	match building_id:
		"dojo":
			show_dojo_menu()
		"shop":
			show_shop_menu()
		"inn":
			show_inn_menu()
		"mission":
			show_mission_menu()
		"blacksmith":
			show_blacksmith_menu()

func _on_building_hover(button: Button):
	var tween = create_tween()
	tween.tween_property(button, "modulate", Color(1.2, 1.2, 1.2), 0.2)

func _on_building_unhover(button: Button):
	var tween = create_tween()
	tween.tween_property(button, "modulate", Color.WHITE, 0.2)

func _on_leave_village():
	print("🔙 Quitter le village")
	emit_signal("leave_village")

# Menus des bâtiments
func show_dojo_menu():
	var menu = create_building_menu("🥋 DOJO D'ENTRAÎNEMENT")
	
	var options = [
		{"text": "💪 Entraînement de Base (+50 XP)", "action": "basic_training"},
		{"text": "⚔️ Entraînement Avancé (+100 XP)", "action": "advanced_training"},
		{"text": "🧘 Méditation (Restaurer Chakra)", "action": "meditation"},
		{"text": "📚 Apprendre Techniques", "action": "learn_jutsu"}
	]
	
	add_menu_options(menu, options)

func show_shop_menu():
	var menu = create_building_menu("🏪 BOUTIQUE DU VILLAGE")
	
	var options = [
		{"text": "🧪 Potion de Santé (50 Ryō)", "action": "buy_health_potion"},
		{"text": "💊 Pilule de Chakra (30 Ryō)", "action": "buy_chakra_pill"},
		{"text": "⭐ Shuriken (20 Ryō)", "action": "buy_shuriken"},
		{"text": "💨 Bombe Fumigène (40 Ryō)", "action": "buy_smoke_bomb"}
	]
	
	add_menu_options(menu, options)

func show_inn_menu():
	var menu = create_building_menu("🏨 AUBERGE DU VILLAGE")
	
	var options = [
		{"text": "🛏️ Se Reposer (Restaurer HP)", "action": "rest"},
		{"text": "💾 Sauvegarder la Partie", "action": "save_game"},
		{"text": "📂 Charger la Partie", "action": "load_game"},
		{"text": "ℹ️ Informations du Village", "action": "village_info"}
	]
	
	add_menu_options(menu, options)

func show_mission_menu():
	var menu = create_building_menu("📜 BUREAU DES MISSIONS")
	
	var options = [
		{"text": "🎯 Mission: Éliminer Bandits", "action": "mission_bandits"},
		{"text": "📦 Mission: Livraison", "action": "mission_delivery"},
		{"text": "🔍 Mission: Reconnaissance", "action": "mission_scout"},
		{"text": "📋 Voir Missions Actives", "action": "active_missions"}
	]
	
	add_menu_options(menu, options)

func show_blacksmith_menu():
	var menu = create_building_menu("⚒️ FORGE DU VILLAGE")
	
	var options = [
		{"text": "🔧 Améliorer Arme (+5 ATT)", "action": "upgrade_weapon"},
		{"text": "🛡️ Améliorer Armure (+3 DEF)", "action": "upgrade_armor"},
		{"text": "⚡ Enchantement Élémentaire", "action": "enchant_equipment"},
		{"text": "🔨 Réparer Équipement", "action": "repair_equipment"}
	]
	
	add_menu_options(menu, options)

func create_building_menu(title: String) -> AcceptDialog:
	var menu = AcceptDialog.new()
	menu.title = title
	menu.size = Vector2(500, 400)
	
	var vbox = VBoxContainer.new()
	menu.add_child(vbox)
	
	return menu

func add_menu_options(menu: AcceptDialog, options: Array):
	var vbox = menu.get_child(0)
	
	for option in options:
		var btn = Button.new()
		btn.text = option.text
		btn.custom_minimum_size = Vector2(450, 50)
		btn.add_theme_font_size_override("font_size", 16)
		btn.pressed.connect(_on_menu_action.bind(option.action, menu))
		vbox.add_child(btn)
	
	# Bouton fermer
	var close_btn = Button.new()
	close_btn.text = "❌ Fermer"
	close_btn.custom_minimum_size = Vector2(450, 50)
	close_btn.pressed.connect(menu.queue_free)
	vbox.add_child(close_btn)
	
	add_child(menu)
	menu.popup_centered()

func _on_menu_action(action: String, menu: AcceptDialog):
	print("🎬 Action: ", action)
	
	match action:
		"basic_training":
			emit_signal("training_started")
			show_message("💪 Entraînement terminé! +50 XP")
		"meditation":
			show_message("🧘 Méditation accomplie! Chakra restauré!")
		"rest":
			show_message("🛏️ Repos réparateur! HP restauré!")
		"save_game":
			show_message("💾 Partie sauvegardée!")
		_:
			show_message("🚧 Fonctionnalité en développement...")
	
	menu.queue_free()

func show_message(text: String):
	var message = Label.new()
	message.text = text
	message.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	message.size = Vector2(400, 60)
	message.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	message.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	message.add_theme_font_size_override("font_size", 18)
	message.add_theme_color_override("font_color", Color.YELLOW)
	message.add_theme_color_override("font_shadow_color", Color.BLACK)
	
	add_child(message)
	
	# Faire disparaître après 3 secondes
	var timer = Timer.new()
	timer.wait_time = 3.0
	timer.one_shot = true
	timer.timeout.connect(func(): message.queue_free(); timer.queue_free())
	add_child(timer)
	timer.start()
