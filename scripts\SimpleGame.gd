extends Node

# Version simplifiée pour tester le jeu rapidement
class_name SimpleGame

var game_state: GameState
var hud: HUD
var player_sprite: Sprite2D
var camera: Camera2D

var player_position: Vector2
var is_moving: bool = false
var movement_speed: float = 200.0

func _ready():
	print("🥷 Shinobi Chronicles - Version Simple")
	setup_game()

func setup_game():
	# Créer le GameState
	game_state = GameState.new()
	add_child(game_state)
	
	# Créer le joueur
	setup_player()
	
	# Créer le HUD
	setup_hud()
	
	# Configurer les inputs
	set_process_input(true)
	
	print("Jeu initialisé ! Utilisez WASD pour bouger, F pour scanner.")
	print("Clan: ", GameConfig.get_clan_data(game_state.player_data.clan).name)

func setup_player():
	# Créer le sprite du joueur
	player_sprite = Sprite2D.new()
	player_sprite.name = "Player"
	
	# Créer une texture simple (carré rouge)
	var image = Image.create(32, 32, false, Image.FORMAT_RGB8)
	image.fill(Color.RED)
	var texture = ImageTexture.new()
	texture.set_image(image)
	player_sprite.texture = texture
	
	add_child(player_sprite)
	
	# Position initiale
	player_position = game_state.player_data.position * GameConfig.WORLD_CONFIG.tile_size
	player_sprite.position = player_position
	
	# Créer la caméra
	camera = Camera2D.new()
	camera.name = "Camera"
	camera.enabled = true
	player_sprite.add_child(camera)

func setup_hud():
	# Créer le HUD
	hud = HUD.new()
	hud.name = "HUD"
	add_child(hud)
	
	# Attendre que le HUD soit prêt puis l'initialiser
	await hud.hud_ready
	hud.initialize(game_state)

func _input(event):
	if is_moving:
		return
	
	var movement_vector = Vector2.ZERO
	
	# Gestion des mouvements
	if Input.is_action_pressed("move_up"):
		movement_vector.y = -1
	elif Input.is_action_pressed("move_down"):
		movement_vector.y = 1
	elif Input.is_action_pressed("move_left"):
		movement_vector.x = -1
	elif Input.is_action_pressed("move_right"):
		movement_vector.x = 1
	
	if movement_vector != Vector2.ZERO:
		move_player(movement_vector)
	
	# Actions spéciales
	if Input.is_action_just_pressed("scan"):
		scan_area()
	elif Input.is_action_just_pressed("interact"):
		interact()
	elif Input.is_action_just_pressed("ui_cancel"):
		show_pause_menu()

func move_player(direction: Vector2):
	var new_world_pos = game_state.player_data.position + direction
	
	# Vérifier les limites du monde
	if new_world_pos.x < 0 or new_world_pos.x >= GameConfig.WORLD_CONFIG.map_width:
		return
	if new_world_pos.y < 0 or new_world_pos.y >= GameConfig.WORLD_CONFIG.map_height:
		return
	
	# Vérifier si le joueur a assez de chakra
	if not game_state.use_chakra(GameConfig.CHAKRA_CONFIG.movement_cost):
		show_message("Pas assez de chakra pour bouger!")
		return
	
	# Déplacer le joueur
	is_moving = true
	var new_pixel_pos = new_world_pos * GameConfig.WORLD_CONFIG.tile_size
	
	# Animation de mouvement
	var tween = create_tween()
	tween.tween_property(player_sprite, "position", new_pixel_pos, 0.2)
	await tween.finished
	
	# Mettre à jour la position dans le game state
	game_state.player_data.position = new_world_pos
	player_position = new_pixel_pos
	
	# Marquer la tuile comme explorée
	game_state.explore_tile(new_world_pos)
	
	# Vérifier les rencontres (chance réduite pour les tests)
	if randf() < 0.1:  # 10% de chance
		trigger_encounter()
	
	is_moving = false

func scan_area():
	if not game_state.use_chakra(GameConfig.CHAKRA_CONFIG.scan_cost):
		show_message("Pas assez de chakra pour scanner!")
		return
	
	# Révéler une zone autour du joueur
	var scan_radius = GameConfig.WORLD_CONFIG.fog_of_war_radius + 1
	var player_tile = game_state.player_data.position
	
	for x in range(-scan_radius, scan_radius + 1):
		for y in range(-scan_radius, scan_radius + 1):
			var tile_pos = player_tile + Vector2(x, y)
			if tile_pos.x >= 0 and tile_pos.x < GameConfig.WORLD_CONFIG.map_width and \
			   tile_pos.y >= 0 and tile_pos.y < GameConfig.WORLD_CONFIG.map_height:
				game_state.explore_tile(tile_pos)
	
	show_message("Zone scannée! (Rayon: " + str(scan_radius) + ")")

func trigger_encounter():
	var enemies = ["wild_boar", "forest_wolf"]
	var enemy_id = enemies[randi() % enemies.size()]
	
	show_message("Rencontre avec " + enemy_id + "! (Combat simplifié)")
	
	# Combat simplifié - juste gagner de l'XP
	game_state.gain_xp(25)
	game_state.add_item("chakra_pill", 1)

func interact():
	show_message("Rien à interagir ici.")

func show_message(text: String):
	if hud:
		hud.show_message(text)
	else:
		print("Message: ", text)

func show_pause_menu():
	var pause_menu = AcceptDialog.new()
	pause_menu.title = "Menu de Pause"
	pause_menu.size = Vector2(300, 250)
	
	var vbox = VBoxContainer.new()
	pause_menu.add_child(vbox)
	
	# Informations du joueur
	var info_label = Label.new()
	info_label.text = "Niveau: " + str(game_state.player_data.level) + "\n"
	info_label.text += "HP: " + str(game_state.player_data.hp) + "/" + str(game_state.player_data.max_hp) + "\n"
	info_label.text += "Chakra: " + str(game_state.player_data.chakra) + "/" + str(game_state.player_data.max_chakra) + "\n"
	info_label.text += "Position: " + str(game_state.player_data.position)
	vbox.add_child(info_label)
	
	var separator = HSeparator.new()
	vbox.add_child(separator)
	
	# Bouton Continuer
	var continue_button = Button.new()
	continue_button.text = "Continuer"
	continue_button.pressed.connect(pause_menu.queue_free)
	vbox.add_child(continue_button)
	
	# Bouton Test Combat
	var combat_button = Button.new()
	combat_button.text = "Test Combat"
	combat_button.pressed.connect(_on_test_combat.bind(pause_menu))
	vbox.add_child(combat_button)
	
	# Bouton Gagner XP
	var xp_button = Button.new()
	xp_button.text = "Gagner 100 XP"
	xp_button.pressed.connect(_on_gain_xp.bind(pause_menu))
	vbox.add_child(xp_button)
	
	# Bouton Quitter
	var quit_button = Button.new()
	quit_button.text = "Quitter"
	quit_button.pressed.connect(get_tree().quit)
	vbox.add_child(quit_button)
	
	add_child(pause_menu)
	pause_menu.popup_centered()

func _on_test_combat(menu: AcceptDialog):
	show_message("Test de combat - Vous gagnez automatiquement!")
	game_state.gain_xp(50)
	game_state.add_item("health_potion", 1)
	menu.queue_free()

func _on_gain_xp(menu: AcceptDialog):
	game_state.gain_xp(100)
	show_message("100 XP gagnés!")
	menu.queue_free()
