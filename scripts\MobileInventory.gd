extends Control

# Inventaire mobile pour Shinobi Chronicles

var player_data: Dictionary
var inventory: Dictionary

# UI Elements
var background: Panel
var title_label: Label
var items_grid: GridContainer
var bottom_bar: Panel

# Signaux
signal item_used(item_id: String)
signal close_inventory

func _ready():
	print("🎒 Inventaire mobile initialisé")
	setup_inventory_ui()

func initialize(p_data: Dictionary, inv: Dictionary):
	self.player_data = p_data
	self.inventory = inv

	# Mettre à jour l'affichage
	update_inventory_display()

func setup_inventory_ui():
	set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	# Arrière-plan
	background = Panel.new()
	background.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	background.modulate = Color(0.1, 0.1, 0.2, 0.95)
	add_child(background)
	
	# Titre
	title_label = Label.new()
	title_label.text = "🎒 INVENTAIRE NINJA"
	title_label.set_anchors_and_offsets_preset(Control.PRESET_CENTER_TOP)
	title_label.position.y = 50
	title_label.size = Vector2(400, 60)
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 24)
	title_label.add_theme_color_override("font_color", Color.GOLD)
	add_child(title_label)
	
	# Container scrollable pour les objets
	var scroll = ScrollContainer.new()
	scroll.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	scroll.size = Vector2(600, 800)
	scroll.position.y = -100
	add_child(scroll)
	
	items_grid = GridContainer.new()
	items_grid.columns = 3
	items_grid.add_theme_constant_override("h_separation", 15)
	items_grid.add_theme_constant_override("v_separation", 15)
	scroll.add_child(items_grid)
	
	# Barre du bas
	create_bottom_bar()

func create_bottom_bar():
	bottom_bar = Panel.new()
	bottom_bar.set_anchors_and_offsets_preset(Control.PRESET_BOTTOM_WIDE)
	bottom_bar.size.y = 80
	bottom_bar.modulate = Color(0, 0, 0, 0.8)
	add_child(bottom_bar)
	
	var hbox = HBoxContainer.new()
	hbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	hbox.alignment = BoxContainer.ALIGNMENT_CENTER
	bottom_bar.add_child(hbox)
	
	# Bouton fermer
	var close_btn = Button.new()
	close_btn.text = "❌ FERMER"
	close_btn.custom_minimum_size = Vector2(200, 60)
	close_btn.add_theme_font_size_override("font_size", 18)
	close_btn.pressed.connect(_on_close_pressed)
	hbox.add_child(close_btn)

func update_inventory_display():
	# Nettoyer la grille
	for child in items_grid.get_children():
		child.queue_free()
	
	# Ajouter les objets
	if inventory.is_empty():
		var empty_label = Label.new()
		empty_label.text = "🍃 Inventaire vide"
		empty_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		empty_label.add_theme_font_size_override("font_size", 18)
		empty_label.add_theme_color_override("font_color", Color.GRAY)
		items_grid.add_child(empty_label)
	else:
		for item_id in inventory.keys():
			create_item_slot(item_id, inventory[item_id])

func create_item_slot(item_id: String, quantity: int):
	var item_button = Button.new()
	item_button.custom_minimum_size = Vector2(180, 120)
	
	# Données de l'objet
	var item_data = get_item_data(item_id)
	
	# Texte du bouton
	item_button.text = item_data.icon + "\n" + item_data.name + "\nx" + str(quantity)
	item_button.add_theme_font_size_override("font_size", 14)
	
	# Couleur selon la rareté
	item_button.add_theme_color_override("font_color", item_data.color)
	
	# Action
	item_button.pressed.connect(_on_item_selected.bind(item_id, item_data))
	
	items_grid.add_child(item_button)

func get_item_data(item_id: String) -> Dictionary:
	# Base de données des objets
	var items_db = {
		"health_potion": {
			"name": "Potion de Santé",
			"icon": "🧪",
			"desc": "Restaure 30 HP",
			"color": Color.GREEN,
			"type": "consumable"
		},
		"chakra_pill": {
			"name": "Pilule de Chakra",
			"icon": "💊",
			"desc": "Restaure 15 Chakra",
			"color": Color.BLUE,
			"type": "consumable"
		},
		"shuriken": {
			"name": "Shuriken",
			"icon": "⭐",
			"desc": "Arme de lancer ninja",
			"color": Color.GRAY,
			"type": "weapon"
		},
		"smoke_bomb": {
			"name": "Bombe Fumigène",
			"icon": "💨",
			"desc": "Permet de fuir un combat",
			"color": Color.PURPLE,
			"type": "tool"
		}
	}
	
	return items_db.get(item_id, {
		"name": "Objet Inconnu",
		"icon": "❓",
		"desc": "Objet mystérieux",
		"color": Color.WHITE,
		"type": "misc"
	})

func _on_item_selected(item_id: String, item_data: Dictionary):
	print("🎯 Objet sélectionné: ", item_id)
	
	# Créer un menu d'action pour l'objet
	show_item_menu(item_id, item_data)

func show_item_menu(item_id: String, item_data: Dictionary):
	var menu = AcceptDialog.new()
	menu.title = item_data.icon + " " + item_data.name
	menu.size = Vector2(400, 300)
	
	var vbox = VBoxContainer.new()
	menu.add_child(vbox)
	
	# Description
	var desc_label = Label.new()
	desc_label.text = item_data.desc
	desc_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	desc_label.add_theme_font_size_override("font_size", 16)
	desc_label.add_theme_color_override("font_color", Color.WHITE)
	desc_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	vbox.add_child(desc_label)
	
	# Quantité
	var quantity_label = Label.new()
	quantity_label.text = "Quantité: " + str(inventory.get(item_id, 0))
	quantity_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	quantity_label.add_theme_font_size_override("font_size", 14)
	quantity_label.add_theme_color_override("font_color", Color.YELLOW)
	vbox.add_child(quantity_label)
	
	# Actions selon le type
	if item_data.type == "consumable":
		var use_btn = Button.new()
		use_btn.text = "✅ UTILISER"
		use_btn.custom_minimum_size = Vector2(350, 50)
		use_btn.add_theme_font_size_override("font_size", 16)
		use_btn.pressed.connect(_on_use_item.bind(item_id, menu))
		vbox.add_child(use_btn)
	
	# Bouton info
	var info_btn = Button.new()
	info_btn.text = "ℹ️ DÉTAILS"
	info_btn.custom_minimum_size = Vector2(350, 50)
	info_btn.add_theme_font_size_override("font_size", 16)
	info_btn.pressed.connect(_on_item_info.bind(item_id, menu))
	vbox.add_child(info_btn)
	
	# Bouton fermer
	var close_btn = Button.new()
	close_btn.text = "❌ FERMER"
	close_btn.custom_minimum_size = Vector2(350, 50)
	close_btn.add_theme_font_size_override("font_size", 16)
	close_btn.pressed.connect(menu.queue_free)
	vbox.add_child(close_btn)
	
	add_child(menu)
	menu.popup_centered()

func _on_use_item(item_id: String, menu: AcceptDialog):
	print("✅ Utilisation de l'objet: ", item_id)
	
	# Vérifier si l'objet existe encore
	if not inventory.has(item_id) or inventory[item_id] <= 0:
		show_message("❌ Objet non disponible!")
		menu.queue_free()
		return
	
	# Utiliser l'objet
	emit_signal("item_used", item_id)
	
	# Mettre à jour l'inventaire local
	inventory[item_id] -= 1
	if inventory[item_id] <= 0:
		inventory.erase(item_id)
	
	# Mettre à jour l'affichage
	update_inventory_display()
	
	# Message de confirmation
	var item_data = get_item_data(item_id)
	show_message("✅ " + item_data.name + " utilisé!")
	
	menu.queue_free()

func _on_item_info(item_id: String, menu: AcceptDialog):
	var item_data = get_item_data(item_id)
	
	var info_text = "📋 INFORMATIONS DÉTAILLÉES\n\n"
	info_text += "Nom: " + item_data.name + "\n"
	info_text += "Type: " + item_data.type.capitalize() + "\n"
	info_text += "Description: " + item_data.desc + "\n"
	info_text += "Quantité: " + str(inventory.get(item_id, 0)) + "\n"
	
	# Ajouter des infos spécifiques selon le type
	match item_data.type:
		"consumable":
			info_text += "\n💡 Peut être utilisé en combat"
		"weapon":
			info_text += "\n⚔️ Équipement d'attaque"
		"tool":
			info_text += "\n🔧 Objet utilitaire"
	
	show_message(info_text)
	menu.queue_free()

func show_message(text: String):
	var message_dialog = AcceptDialog.new()
	message_dialog.dialog_text = text
	message_dialog.title = "📢 Information"
	add_child(message_dialog)
	message_dialog.popup_centered()
	
	# Auto-fermer après 3 secondes pour les messages courts
	if text.length() < 50:
		var timer = Timer.new()
		timer.wait_time = 3.0
		timer.one_shot = true
		timer.timeout.connect(func(): 
			if message_dialog and is_instance_valid(message_dialog):
				message_dialog.queue_free()
			timer.queue_free()
		)
		add_child(timer)
		timer.start()

func _on_close_pressed():
	print("❌ Fermeture de l'inventaire")
	emit_signal("close_inventory")
