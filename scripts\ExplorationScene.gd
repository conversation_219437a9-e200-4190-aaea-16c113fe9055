extends Node2D

# Scène d'exploration principale pour Shinobi Chronicles
class_name ExplorationScene

@onready var player_sprite: Sprite2D
@onready var camera: Camera2D
@onready var tilemap: TileMap
@onready var fog_of_war: Node2D
@onready var hud: HUD
@onready var battle_scene: BattleScene

var game_state: GameState
var combat_system: CombatSystem
var save_system: SaveSystem

var player_position: Vector2
var is_moving: bool = false
var movement_speed: float = 200.0

# Système de rencontres
var encounter_timer: float = 0.0
var base_encounter_rate: float = 0.3

signal encounter_triggered(enemy_id)
signal region_changed(new_region)

func _ready():
	# Initialiser les systèmes
	setup_game_systems()
	setup_player()
	setup_camera()
	setup_hud()
	setup_input()

	# Connecter les signaux
	connect_signals()

func setup_game_systems():
	# Créer les instances des systèmes de jeu
	game_state = GameState.new()
	combat_system = CombatSystem.new()
	save_system = SaveSystem.new()
	
	add_child(game_state)
	add_child(combat_system)
	add_child(save_system)

func setup_player():
	# Créer le sprite du joueur
	player_sprite = Sprite2D.new()
	player_sprite.name = "PlayerSprite"
	
	# Créer une texture temporaire (carré rouge)
	var image = Image.create(32, 32, false, Image.FORMAT_RGB8)
	image.fill(Color.RED)
	var texture = ImageTexture.new()
	texture.set_image(image)
	player_sprite.texture = texture
	
	add_child(player_sprite)
	
	# Position initiale
	player_position = game_state.player_data.position * GameConfig.WORLD_CONFIG.tile_size
	player_sprite.position = player_position

func setup_camera():
	# Créer la caméra
	camera = Camera2D.new()
	camera.name = "Camera"
	camera.enabled = true
	player_sprite.add_child(camera)

func setup_hud():
	# Créer le HUD
	hud = HUD.new()
	hud.name = "HUD"
	add_child(hud)

	# Attendre que le HUD soit prêt puis l'initialiser
	await hud.hud_ready
	hud.initialize(game_state)

func setup_input():
	# Activer le traitement des inputs
	set_process_input(true)
	set_process(true)

func connect_signals():
	# Connecter les signaux du système de combat
	combat_system.combat_started.connect(_on_combat_started)
	combat_system.combat_ended.connect(_on_combat_ended)
	
	# Connecter les signaux du game state
	game_state.player_stats_changed.connect(_on_player_stats_changed)
	game_state.chakra_changed.connect(_on_chakra_changed)
	game_state.level_up.connect(_on_level_up)

func _input(event):
	if is_moving:
		return
	
	var movement_vector = Vector2.ZERO
	
	# Gestion des mouvements
	if Input.is_action_pressed("move_up"):
		movement_vector.y = -1
	elif Input.is_action_pressed("move_down"):
		movement_vector.y = 1
	elif Input.is_action_pressed("move_left"):
		movement_vector.x = -1
	elif Input.is_action_pressed("move_right"):
		movement_vector.x = 1
	
	if movement_vector != Vector2.ZERO:
		move_player(movement_vector)
	
	# Actions spéciales
	if Input.is_action_just_pressed("scan"):
		scan_area()
	elif Input.is_action_just_pressed("interact"):
		interact()
	elif Input.is_action_just_pressed("inventory"):
		open_inventory()
	elif Input.is_action_just_pressed("skills"):
		open_skills()
	elif Input.is_action_just_pressed("map"):
		open_map()

func _process(delta):
	# Mettre à jour le timer de rencontres
	if not is_moving:
		encounter_timer += delta

func move_player(direction: Vector2):
	var new_world_pos = game_state.player_data.position + direction
	
	# Vérifier les limites du monde
	if new_world_pos.x < 0 or new_world_pos.x >= GameConfig.WORLD_CONFIG.map_width:
		return
	if new_world_pos.y < 0 or new_world_pos.y >= GameConfig.WORLD_CONFIG.map_height:
		return
	
	# Vérifier si le joueur a assez de chakra
	if not game_state.use_chakra(GameConfig.CHAKRA_CONFIG.movement_cost):
		show_message("Pas assez de chakra pour bouger!")
		return
	
	# Déplacer le joueur
	is_moving = true
	var new_pixel_pos = new_world_pos * GameConfig.WORLD_CONFIG.tile_size
	
	# Animation de mouvement
	var tween = create_tween()
	tween.tween_property(player_sprite, "position", new_pixel_pos, 0.2)
	await tween.finished
	
	# Mettre à jour la position dans le game state
	game_state.player_data.position = new_world_pos
	player_position = new_pixel_pos
	
	# Marquer la tuile comme explorée
	game_state.explore_tile(new_world_pos)
	
	# Vérifier les rencontres
	check_encounter()
	
	# Vérifier le changement de région
	check_region_change()
	
	is_moving = false

func scan_area():
	if not game_state.use_chakra(GameConfig.CHAKRA_CONFIG.scan_cost):
		show_message("Pas assez de chakra pour scanner!")
		return
	
	# Révéler une zone autour du joueur
	var scan_radius = GameConfig.WORLD_CONFIG.fog_of_war_radius + 1
	var player_tile = game_state.player_data.position
	
	for x in range(-scan_radius, scan_radius + 1):
		for y in range(-scan_radius, scan_radius + 1):
			var tile_pos = player_tile + Vector2(x, y)
			if tile_pos.x >= 0 and tile_pos.x < GameConfig.WORLD_CONFIG.map_width and \
			   tile_pos.y >= 0 and tile_pos.y < GameConfig.WORLD_CONFIG.map_height:
				game_state.explore_tile(tile_pos)
	
	show_message("Zone scannée!")

func check_encounter():
	var current_region = get_current_region()
	var region_data = GameConfig.get_region_data(current_region)
	
	if region_data.is_empty():
		return
	
	var encounter_rate = region_data.get("encounter_rate", base_encounter_rate)
	
	# Augmenter les chances avec le temps
	var time_bonus = min(encounter_timer * 0.1, 0.5)
	encounter_rate += time_bonus
	
	if randf() < encounter_rate:
		trigger_encounter(current_region)
		encounter_timer = 0.0

func trigger_encounter(region_id: String):
	var region_data = GameConfig.get_region_data(region_id)
	var level_range = region_data.get("level_range", [1, 3])
	
	# Choisir un ennemi aléatoire approprié au niveau
	var possible_enemies = get_enemies_for_level_range(level_range)
	
	if possible_enemies.size() > 0:
		var enemy_id = possible_enemies[randi() % possible_enemies.size()]
		emit_signal("encounter_triggered", enemy_id)
		start_combat(enemy_id)

func get_enemies_for_level_range(level_range: Array) -> Array:
	var enemies = []

	# Utiliser le data loader si disponible
	if game_state and game_state.get_parent() and game_state.get_parent().has_method("get_data_loader"):
		var data_loader = game_state.get_parent().get_data_loader()
		if data_loader:
			enemies = data_loader.get_enemies_by_level_range(level_range[0], level_range[1])

	# Fallback vers des ennemis de base
	if enemies.is_empty():
		match level_range[1]:
			1, 2, 3:
				enemies = ["wild_boar"]
			4, 5, 6:
				enemies = ["wild_boar", "forest_wolf"]
			_:
				enemies = ["wild_boar", "forest_wolf", "shadow_ninja"]

	return enemies

func start_combat(enemy_id: String):
	# Démarrer le combat
	combat_system.start_combat(enemy_id, game_state)
	
	# Ici, on pourrait changer de scène vers une scène de combat
	# Pour l'instant, on reste dans la même scène
	show_message("Combat contre " + enemy_id + " !")

func get_current_region() -> String:
	# Déterminer la région actuelle basée sur la position
	var pos = game_state.player_data.position
	
	# Logique simple pour déterminer la région
	if pos.x < 20 and pos.y < 20:
		return "starting_village"
	elif pos.x < 50 and pos.y < 50:
		return "forest_outskirts"
	elif pos.x < 70 and pos.y < 70:
		return "dark_woods"
	else:
		return "mountain_path"

func check_region_change():
	var new_region = get_current_region()
	
	if new_region != game_state.player_data.current_region:
		# Vérifier si la région est débloquée
		if new_region not in game_state.unlocked_regions:
			var region_data = GameConfig.get_region_data(new_region)
			var unlock_level = region_data.get("unlock_level", 1)
			
			if game_state.player_data.level >= unlock_level:
				game_state.unlock_region(new_region)
				show_message("Nouvelle région débloquée: " + region_data.get("name", new_region))
			else:
				show_message("Région verrouillée. Niveau " + str(unlock_level) + " requis.")
				return
		
		game_state.player_data.current_region = new_region
		emit_signal("region_changed", new_region)

func interact():
	# Logique d'interaction avec l'environnement
	show_message("Rien à interagir ici.")

func open_inventory():
	show_message("Inventaire (à implémenter)")

func open_skills():
	show_message("Compétences (à implémenter)")

func open_map():
	show_message("Carte (à implémenter)")

func show_message(text: String):
	if hud:
		hud.show_message(text)
	else:
		print("Message: ", text)

# Callbacks des signaux
func _on_combat_started(enemy_data):
	print("Combat commencé contre: ", enemy_data.name)

func _on_combat_ended(victory: bool, rewards: Dictionary):
	if victory:
		var message = "Victoire! XP: " + str(rewards.get("xp", 0))
		if rewards.has("loot") and rewards.loot.size() > 0:
			message += " Loot obtenu!"
		show_message(message)
	else:
		show_message("Défaite...")

func _on_player_stats_changed():
	# Mettre à jour l'interface utilisateur
	pass

func _on_chakra_changed():
	# Mettre à jour l'affichage du chakra
	pass

func _on_level_up(new_level: int):
	show_message("Niveau supérieur! Nouveau niveau: " + str(new_level))

# Initialiser avec les systèmes externes
func initialize_with_systems(external_game_state: GameState, external_combat_system: CombatSystem, external_save_system: SaveSystem):
	# Remplacer les systèmes locaux par les systèmes externes
	if game_state:
		game_state.queue_free()
	if combat_system:
		combat_system.queue_free()
	if save_system:
		save_system.queue_free()

	game_state = external_game_state
	combat_system = external_combat_system
	save_system = external_save_system

	# Reconnecter les signaux
	connect_signals()

	# Mettre à jour la position du joueur
	player_position = game_state.player_data.position * GameConfig.WORLD_CONFIG.tile_size
	player_sprite.position = player_position

	# Initialiser le HUD si il existe
	if hud:
		hud.initialize(game_state)

# Fonctions de sauvegarde/chargement
func save_game(slot: int):
	save_system.save_game(slot, game_state)

func load_game(slot: int):
	if save_system.load_game(slot, game_state):
		# Mettre à jour la position du joueur
		player_position = game_state.player_data.position * GameConfig.WORLD_CONFIG.tile_size
		player_sprite.position = player_position

# Rafraîchir depuis le game state (après chargement)
func refresh_from_game_state():
	if game_state:
		player_position = game_state.player_data.position * GameConfig.WORLD_CONFIG.tile_size
		player_sprite.position = player_position

		if hud:
			hud.update_player_info()
			hud.update_minimap_position()
