extends Node

# Configuration centralisée pour Shinobi Chronicles
class_name GameConfig

# Constantes du jeu
const GAME_VERSION = "1.0.0"
const SAVE_SLOTS = 3

# Configuration du monde
const WORLD_CONFIG = {
	"tile_size": 32,
	"map_width": 100,
	"map_height": 100,
	"fog_of_war_radius": 3,
	"encounter_rate": 0.3
}

# Configuration du chakra
const CHAKRA_CONFIG = {
	"base_amount": 20,
	"regen_rate": 1.0,  # par minute
	"movement_cost": 1,
	"scan_cost": 5
}

# Configuration de progression
const PROGRESSION_CONFIG = {
	"base_xp": 100,
	"xp_multiplier": 1.5,
	"max_level": 50
}

# Configuration de combat
const COMBAT_CONFIG = {
	"damage_multipliers": {
		"weakness": 1.5,
		"resistance": 0.75,
		"neutral": 1.0
	},
	"status_duration": 3  # tours
}

# Éléments et leurs relations
const ELEMENTS = {
	"fire": {
		"name": "<PERSON><PERSON>",
		"strong_against": ["wind"],
		"weak_against": ["water"],
		"color": Color.RED
	},
	"water": {
		"name": "Eau", 
		"strong_against": ["fire"],
		"weak_against": ["earth"],
		"color": Color.BLUE
	},
	"earth": {
		"name": "Terre",
		"strong_against": ["water"],
		"weak_against": ["wind"],
		"color": Color(0.6, 0.4, 0.2)
	},
	"wind": {
		"name": "Vent",
		"strong_against": ["earth"],
		"weak_against": ["fire"],
		"color": Color.CYAN
	},
	"shadow": {
		"name": "Ombre",
		"strong_against": [],
		"weak_against": [],
		"color": Color(0.3, 0.3, 0.3)
	}
}

# Configuration des clans
const CLANS = {
	"ryujin": {
		"name": "Clan Ryūjin",
		"element": "fire",
		"bonus_attack": 2,
		"bonus_resistance": 0.2,
		"special_jutsu": "dragon_slash",
		"description": "Combattant offensif maîtrisant le feu"
	},
	"yurei": {
		"name": "Clan Yūrei", 
		"element": "shadow",
		"bonus_stealth": 3,
		"bonus_genjutsu": 0.3,
		"special_jutsu": "shadow_clone",
		"description": "Assassin furtif maîtrisant les ombres"
	},
	"kaze": {
		"name": "Clan Kaze",
		"element": "wind", 
		"bonus_speed": 2,
		"bonus_wind_power": 0.25,
		"special_jutsu": "wind_blade",
		"description": "Combattant rapide maîtrisant le vent"
	}
}

# Régions du monde
const REGIONS = {
	"starting_village": {
		"name": "Village de Départ",
		"level_range": [1, 3],
		"biome": "village",
		"encounter_rate": 0.1,
		"unlock_level": 1
	},
	"forest_outskirts": {
		"name": "Lisière de la Forêt",
		"level_range": [2, 5],
		"biome": "forest",
		"encounter_rate": 0.3,
		"unlock_level": 2
	},
	"dark_woods": {
		"name": "Bois Sombres",
		"level_range": [4, 8],
		"biome": "dark_forest",
		"encounter_rate": 0.5,
		"unlock_level": 5
	},
	"mountain_path": {
		"name": "Sentier de Montagne",
		"level_range": [6, 12],
		"biome": "mountain",
		"encounter_rate": 0.4,
		"unlock_level": 8
	}
}

# Configuration de debug
var debug_config = {
	"enabled": false,
	"god_mode": false,
	"unlock_all_regions": false,
	"start_level": 1,
	"infinite_chakra": false
}

# Fonction pour obtenir la relation entre deux éléments
static func get_element_effectiveness(attacker_element: String, defender_element: String) -> float:
	if not ELEMENTS.has(attacker_element) or not ELEMENTS.has(defender_element):
		return COMBAT_CONFIG.damage_multipliers.neutral
	
	var attacker_data = ELEMENTS[attacker_element]
	
	if defender_element in attacker_data.strong_against:
		return COMBAT_CONFIG.damage_multipliers.weakness
	elif defender_element in attacker_data.weak_against:
		return COMBAT_CONFIG.damage_multipliers.resistance
	else:
		return COMBAT_CONFIG.damage_multipliers.neutral

# Fonction pour obtenir les données d'un clan
static func get_clan_data(clan_id: String) -> Dictionary:
	return CLANS.get(clan_id, {})

# Fonction pour obtenir les données d'une région
static func get_region_data(region_id: String) -> Dictionary:
	return REGIONS.get(region_id, {})

# Fonction pour calculer l'XP nécessaire pour un niveau
static func get_xp_for_level(level: int) -> int:
	return int(PROGRESSION_CONFIG.base_xp * pow(PROGRESSION_CONFIG.xp_multiplier, level - 1))
