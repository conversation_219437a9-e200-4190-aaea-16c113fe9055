extends Node

# Système de chargement de données pour Shinobi Chronicles
class_name DataLoader

# Cache des données chargées
var enemies_data: Dictionary = {}
var jutsu_data: Dictionary = {}
var items_data: Dictionary = {}

var is_loaded: bool = false

signal data_loaded

func _ready():
	load_all_data()

func load_all_data():
	# Charger toutes les données depuis les fichiers JSON
	load_enemies_data()
	load_jutsu_data()
	load_items_data()
	
	is_loaded = true
	emit_signal("data_loaded")
	print("Toutes les données ont été chargées!")

func load_enemies_data():
	var file_path = "res://data/enemies.json"
	enemies_data = load_json_file(file_path)
	
	if enemies_data.is_empty():
		print("Erreur: Impossible de charger les données d'ennemis")
		# Utiliser les données par défaut du CombatSystem
		enemies_data = get_default_enemies_data()
	else:
		print("Données d'ennemis chargées: ", enemies_data.size(), " ennemis")

func load_jutsu_data():
	var file_path = "res://data/jutsu.json"
	jutsu_data = load_json_file(file_path)
	
	if jutsu_data.is_empty():
		print("Erreur: Impossible de charger les données de jutsu")
		# Utiliser les données par défaut du CombatSystem
		jutsu_data = get_default_jutsu_data()
	else:
		print("Données de jutsu chargées: ", jutsu_data.size(), " jutsu")

func load_items_data():
	var file_path = "res://data/items.json"
	items_data = load_json_file(file_path)
	
	if items_data.is_empty():
		print("Erreur: Impossible de charger les données d'objets")
		# Utiliser des données par défaut
		items_data = get_default_items_data()
	else:
		print("Données d'objets chargées: ", items_data.size(), " objets")

func load_json_file(file_path: String) -> Dictionary:
	if not FileAccess.file_exists(file_path):
		print("Fichier non trouvé: ", file_path)
		return {}
	
	var file = FileAccess.open(file_path, FileAccess.READ)
	if file == null:
		print("Impossible d'ouvrir le fichier: ", file_path)
		return {}
	
	var json_string = file.get_as_text()
	file.close()
	
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	
	if parse_result != OK:
		print("Erreur de parsing JSON dans: ", file_path)
		return {}
	
	return json.data

# Fonctions d'accès aux données
func get_enemy_data(enemy_id: String) -> Dictionary:
	return enemies_data.get(enemy_id, {})

func get_jutsu_data(jutsu_id: String) -> Dictionary:
	return jutsu_data.get(jutsu_id, {})

func get_item_data(item_id: String) -> Dictionary:
	return items_data.get(item_id, {})

func get_all_enemies() -> Dictionary:
	return enemies_data

func get_all_jutsu() -> Dictionary:
	return jutsu_data

func get_all_items() -> Dictionary:
	return items_data

# Fonctions pour obtenir des listes filtrées
func get_enemies_by_level_range(min_level: int, max_level: int) -> Array:
	var filtered_enemies = []
	
	for enemy_id in enemies_data.keys():
		var enemy = enemies_data[enemy_id]
		var level = enemy.get("level", 1)
		
		if level >= min_level and level <= max_level:
			filtered_enemies.append(enemy_id)
	
	return filtered_enemies

func get_jutsu_by_clan(clan_id: String) -> Array:
	var clan_jutsu = []
	
	for jutsu_id in jutsu_data.keys():
		var jutsu = jutsu_data[jutsu_id]
		var clan_req = jutsu.get("clan_requirement", null)
		
		if clan_req == clan_id or clan_req == null:
			clan_jutsu.append(jutsu_id)
	
	return clan_jutsu

func get_jutsu_by_level(level: int) -> Array:
	var available_jutsu = []
	
	for jutsu_id in jutsu_data.keys():
		var jutsu = jutsu_data[jutsu_id]
		var level_req = jutsu.get("level_requirement", 1)
		
		if level >= level_req:
			available_jutsu.append(jutsu_id)
	
	return available_jutsu

func get_items_by_type(item_type: String) -> Array:
	var filtered_items = []
	
	for item_id in items_data.keys():
		var item = items_data[item_id]
		var type = item.get("type", "")
		
		if type == item_type:
			filtered_items.append(item_id)
	
	return filtered_items

func get_items_by_rarity(rarity: String) -> Array:
	var filtered_items = []
	
	for item_id in items_data.keys():
		var item = items_data[item_id]
		var item_rarity = item.get("rarity", "common")
		
		if item_rarity == rarity:
			filtered_items.append(item_id)
	
	return filtered_items

# Données par défaut en cas d'erreur de chargement
func get_default_enemies_data() -> Dictionary:
	return {
		"wild_boar": {
			"name": "Sanglier Sauvage",
			"level": 2,
			"hp": 40,
			"max_hp": 40,
			"attack": 8,
			"defense": 3,
			"speed": 5,
			"element": "earth",
			"xp_reward": 25,
			"ryo_reward": 15,
			"loot": [],
			"ai_pattern": "aggressive"
		}
	}

func get_default_jutsu_data() -> Dictionary:
	return {
		"basic_attack": {
			"name": "Attaque de Base",
			"type": "taijutsu",
			"element": "neutral",
			"chakra_cost": 0,
			"base_damage": 15,
			"status_effect": null,
			"status_duration": 0,
			"clan_requirement": null
		}
	}

func get_default_items_data() -> Dictionary:
	return {
		"chakra_pill": {
			"name": "Pilule de Chakra",
			"type": "consumable",
			"rarity": "common",
			"effect": "restore_chakra",
			"value": 10,
			"price": 25,
			"description": "Restaure 10 points de chakra.",
			"stackable": true,
			"max_stack": 99
		},
		"health_potion": {
			"name": "Potion de Santé",
			"type": "consumable",
			"rarity": "common",
			"effect": "restore_hp",
			"value": 30,
			"price": 50,
			"description": "Restaure 30 points de vie.",
			"stackable": true,
			"max_stack": 99
		}
	}

# Fonction pour recharger les données (utile pour le développement)
func reload_data():
	enemies_data.clear()
	jutsu_data.clear()
	items_data.clear()
	
	load_all_data()

# Fonction pour valider l'intégrité des données
func validate_data() -> bool:
	var is_valid = true
	
	# Vérifier que les données essentielles sont présentes
	if not jutsu_data.has("basic_attack"):
		print("Erreur: jutsu 'basic_attack' manquant")
		is_valid = false
	
	if enemies_data.is_empty():
		print("Erreur: aucun ennemi chargé")
		is_valid = false
	
	if items_data.is_empty():
		print("Erreur: aucun objet chargé")
		is_valid = false
	
	return is_valid
