extends Node

# Shinobi Chronicles - Version Complète et Autonome
# Tous les systèmes intégrés dans un seul fichier pour éviter les problèmes de dépendances

# === SIGNAUX ===
signal player_stats_changed
signal chakra_changed
signal level_up_achieved(new_level)
signal item_obtained(item_id, quantity)

# === DONNÉES DU JOUEUR ===
var player_data = {
	"name": "Ninja",
	"clan": "ryujin",
	"level": 1,
	"xp": 0,
	"hp": 100,
	"max_hp": 100,
	"chakra": 20,
	"max_chakra": 20,
	"attack": 10,
	"defense": 5,
	"speed": 8,
	"stealth": 3,
	"position": Vector2(50, 50),
	"current_region": "starting_village"
}

# === INVENTAIRE ET PROGRESSION ===
var inventory = {}
var learned_jutsu = ["basic_attack"]
var unlocked_regions = ["starting_village"]
var explored_tiles = {}

# === STATISTIQUES DE JEU ===
var game_stats = {
	"battles_won": 0,
	"enemies_defeated": 0,
	"total_damage_dealt": 0,
	"jutsu_used": 0,
	"distance_traveled": 0,
	"play_time": 0.0
}

# === CONFIGURATION LOCALE ===
var config = {
	"chakra_regen_rate": 1.0,
	"movement_cost": 1,
	"scan_cost": 5,
	"base_xp": 100,
	"xp_multiplier": 1.5,
	"max_level": 50,
	"chakra_regen_interval": 8.0  # secondes
}

# === ÉLÉMENTS ET CLANS ===
var elements = {
	"fire": {"name": "Feu", "strong_against": ["wind"], "weak_against": ["water"], "color": Color.RED},
	"water": {"name": "Eau", "strong_against": ["fire"], "weak_against": ["earth"], "color": Color.BLUE},
	"earth": {"name": "Terre", "strong_against": ["water"], "weak_against": ["wind"], "color": Color(0.6, 0.4, 0.2)},
	"wind": {"name": "Vent", "strong_against": ["earth"], "weak_against": ["fire"], "color": Color.CYAN},
	"shadow": {"name": "Ombre", "strong_against": [], "weak_against": [], "color": Color(0.3, 0.3, 0.3)}
}

var clans = {
	"ryujin": {"name": "Clan Ryūjin", "element": "fire", "bonus_attack": 2, "description": "Maîtres du feu"},
	"yurei": {"name": "Clan Yūrei", "element": "shadow", "bonus_stealth": 3, "description": "Assassins de l'ombre"},
	"kaze": {"name": "Clan Kaze", "element": "wind", "bonus_speed": 2, "description": "Guerriers du vent"}
}

# === VARIABLES D'INTERFACE ===
var player_sprite: Sprite2D
var camera: Camera2D
var hud_container: Control
var hp_bar: ProgressBar
var chakra_bar: ProgressBar
var xp_bar: ProgressBar
var level_label: Label
var position_label: Label
var clan_label: Label
var message_label: Label
var stats_label: Label

# === VARIABLES DE JEU ===
var is_moving: bool = false
var chakra_regen_timer: float = 0.0
var message_timer: Timer

func _ready():
	print("🥷 === SHINOBI CHRONICLES - VERSION COMPLÈTE ===")
	print("🎮 Initialisation du jeu ninja...")

	setup_complete_game()

func setup_complete_game():
	# Appliquer les bonus de clan
	apply_clan_bonuses()

	# Créer le joueur ninja
	setup_ninja_player()

	# Créer l'interface ninja complète
	setup_ninja_interface()

	# Configurer les systèmes
	set_process_input(true)
	set_process(true)

	# Connecter les signaux
	connect_ninja_signals()

	print("✅ Jeu ninja initialisé avec succès!")
	print("🏮 Clan: " + clans[player_data.clan].name)
	print("⚔️ Stats: ATT:" + str(player_data.attack) + " DEF:" + str(player_data.defense) + " VIT:" + str(player_data.speed))

	show_ninja_message("🥷 Bienvenue dans Shinobi Chronicles! Votre aventure ninja commence...")

func apply_clan_bonuses():
	var clan_data = clans[player_data.clan]

	match player_data.clan:
		"ryujin":
			player_data.attack += clan_data.bonus_attack
			print("🔥 Bonus Clan Ryūjin: +2 Attaque")
		"yurei":
			player_data.stealth += clan_data.bonus_stealth
			print("👤 Bonus Clan Yūrei: +3 Furtivité")
		"kaze":
			player_data.speed += clan_data.bonus_speed
			print("💨 Bonus Clan Kaze: +2 Vitesse")

func setup_ninja_player():
	# Créer le sprite ninja avec design détaillé
	player_sprite = Sprite2D.new()
	player_sprite.name = "MasterNinja"

	# Créer une texture ninja selon le clan
	var image = Image.create(32, 32, false, Image.FORMAT_RGB8)
	var clan_color = elements[clans[player_data.clan].element].color

	# Design ninja personnalisé selon le clan
	for x in range(32):
		for y in range(32):
			if x == 0 or x == 31 or y == 0 or y == 31:
				image.set_pixel(x, y, Color.BLACK)  # Bordure
			elif y < 10:  # Masque ninja
				image.set_pixel(x, y, Color(0.2, 0.2, 0.2))
			elif (x == 10 or x == 22) and y == 8:  # Yeux
				image.set_pixel(x, y, clan_color)
			elif y >= 20 and y <= 22:  # Ceinture
				image.set_pixel(x, y, Color.BLACK)
			elif (x % 3 == 0 or y % 3 == 0) and x > 2 and x < 29 and y > 10 and y < 20:
				image.set_pixel(x, y, clan_color * 0.7)  # Détails clan
			else:
				image.set_pixel(x, y, clan_color * 0.5)  # Corps principal

	var texture = ImageTexture.new()
	texture.set_image(image)
	player_sprite.texture = texture

	add_child(player_sprite)
	update_ninja_position()

	# Caméra ninja
	camera = Camera2D.new()
	camera.name = "NinjaVision"
	camera.enabled = true
	player_sprite.add_child(camera)

func update_ninja_position():
	var pixel_pos = player_data.position * 32
	player_sprite.position = pixel_pos

func setup_ninja_interface():
	# Container principal ninja
	hud_container = Control.new()
	hud_container.name = "NinjaInterface"
	hud_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	hud_container.mouse_filter = Control.MOUSE_FILTER_IGNORE
	add_child(hud_container)

	# Panel principal avec style ninja
	var main_panel = Panel.new()
	main_panel.position = Vector2(10, 10)
	main_panel.size = Vector2(380, 280)
	main_panel.modulate = Color(0.05, 0.05, 0.1, 0.9)  # Bleu très sombre ninja
	hud_container.add_child(main_panel)

	var vbox = VBoxContainer.new()
	vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	vbox.add_theme_constant_override("separation", 10)
	main_panel.add_child(vbox)

	# Titre ninja épique
	var title_container = HBoxContainer.new()
	vbox.add_child(title_container)

	var title_label = Label.new()
	title_label.text = "🥷 SHINOBI CHRONICLES 🥷"
	title_label.add_theme_color_override("font_color", Color.GOLD)
	title_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	title_container.add_child(title_label)

	level_label = Label.new()
	level_label.text = "忍者 Niv." + str(player_data.level)
	level_label.add_theme_color_override("font_color", Color.CYAN)
	level_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
	title_container.add_child(level_label)

	# Barres de stats ninja
	create_ninja_stat_bar(vbox, "❤️ ÉNERGIE VITALE", player_data.hp, player_data.max_hp, Color.RED)
	hp_bar = vbox.get_child(vbox.get_child_count() - 1).get_child(1)

	create_ninja_stat_bar(vbox, "🔵 CHAKRA NINJA", player_data.chakra, player_data.max_chakra, Color.CYAN)
	chakra_bar = vbox.get_child(vbox.get_child_count() - 1).get_child(1)

	var current_xp = get_xp_for_current_level()
	var needed_xp = get_xp_for_next_level()
	create_ninja_stat_bar(vbox, "⭐ EXPÉRIENCE", current_xp, needed_xp, Color.YELLOW)
	xp_bar = vbox.get_child(vbox.get_child_count() - 1).get_child(1)

	# Informations ninja
	clan_label = Label.new()
	clan_label.text = "🏮 " + clans[player_data.clan].name + " (" + clans[player_data.clan].description + ")"
	clan_label.add_theme_color_override("font_color", elements[clans[player_data.clan].element].color)
	clan_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(clan_label)

	position_label = Label.new()
	position_label.text = "📍 Territoire: " + str(player_data.position)
	position_label.add_theme_color_override("font_color", Color.LIGHT_GREEN)
	position_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(position_label)

	stats_label = Label.new()
	update_ninja_stats_display()
	stats_label.add_theme_color_override("font_color", Color.WHITE)
	stats_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(stats_label)

	# Zone de messages ninja
	message_label = Label.new()
	message_label.position = Vector2(10, 310)
	message_label.size = Vector2(600, 80)
	message_label.add_theme_color_override("font_color", Color.YELLOW)
	message_label.add_theme_color_override("font_shadow_color", Color.BLACK)
	message_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	message_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	message_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	hud_container.add_child(message_label)

	# Guide des contrôles ninja
	var controls_panel = Panel.new()
	controls_panel.position = Vector2(get_viewport().size.x - 280, 10)
	controls_panel.size = Vector2(270, 150)
	controls_panel.modulate = Color(0.1, 0.05, 0.05, 0.8)
	hud_container.add_child(controls_panel)

	var controls_label = Label.new()
	controls_label.text = "🎮 TECHNIQUES NINJA:\n\n"
	controls_label.text += "WASD: Déplacement Furtif (1 chakra)\n"
	controls_label.text += "F: Technique de Scan (5 chakra)\n"
	controls_label.text += "E: Fouille Ninja\n"
	controls_label.text += "Échap: Dojo du Ninja\n\n"
	controls_label.text += "🥷 Maîtrisez l'art ninja!"
	controls_label.add_theme_color_override("font_color", Color.LIGHT_GRAY)
	controls_label.position = Vector2(10, 10)
	controls_label.size = Vector2(250, 130)
	controls_panel.add_child(controls_label)

	# Timer pour messages
	message_timer = Timer.new()
	message_timer.wait_time = 5.0
	message_timer.one_shot = true
	message_timer.timeout.connect(_on_ninja_message_timeout)
	add_child(message_timer)

func create_ninja_stat_bar(parent: VBoxContainer, label_text: String, current: int, maximum: int, color: Color):
	var container = HBoxContainer.new()
	parent.add_child(container)

	var label = Label.new()
	label.text = label_text
	label.add_theme_color_override("font_color", Color.WHITE)
	label.custom_minimum_size.x = 120
	container.add_child(label)

	var bar = ProgressBar.new()
	bar.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	bar.show_percentage = false
	bar.modulate = color
	bar.max_value = maximum
	bar.value = current
	container.add_child(bar)

	var value_label = Label.new()
	value_label.text = str(current) + "/" + str(maximum)
	value_label.add_theme_color_override("font_color", color)
	value_label.custom_minimum_size.x = 70
	container.add_child(value_label)

func update_ninja_stats_display():
	if stats_label:
		stats_label.text = "⚔️ ATT:" + str(player_data.attack) + " 🛡️ DEF:" + str(player_data.defense) + " ⚡ VIT:" + str(player_data.speed) + " 👤 FUR:" + str(player_data.stealth)

func connect_ninja_signals():
	player_stats_changed.connect(_on_ninja_stats_changed)
	chakra_changed.connect(_on_ninja_chakra_changed)
	level_up_achieved.connect(_on_ninja_level_up)
	item_obtained.connect(_on_ninja_item_obtained)

func _process(delta):
	# Régénération de chakra ninja
	chakra_regen_timer += delta
	if chakra_regen_timer >= config.chakra_regen_interval:
		regenerate_ninja_chakra()
		chakra_regen_timer = 0.0

	# Temps de jeu
	game_stats.play_time += delta

func regenerate_ninja_chakra():
	if player_data.chakra < player_data.max_chakra:
		var regen_amount = config.chakra_regen_rate
		player_data.chakra = min(player_data.chakra + regen_amount, player_data.max_chakra)
		emit_signal("chakra_changed")
		print("💨 Chakra ninja régénéré: ", player_data.chakra, "/", player_data.max_chakra)

# === FONCTIONS DE GAMEPLAY NINJA ===

func _input(_event):
	if is_moving:
		return

	var movement_vector = Vector2.ZERO

	# Mouvements ninja furtifs
	if Input.is_action_pressed("move_up"):
		movement_vector.y = -1
	elif Input.is_action_pressed("move_down"):
		movement_vector.y = 1
	elif Input.is_action_pressed("move_left"):
		movement_vector.x = -1
	elif Input.is_action_pressed("move_right"):
		movement_vector.x = 1

	if movement_vector != Vector2.ZERO:
		execute_ninja_movement(movement_vector)

	# Techniques ninja spéciales
	if Input.is_action_just_pressed("scan"):
		execute_ninja_scan()
	elif Input.is_action_just_pressed("interact"):
		execute_ninja_search()
	elif Input.is_action_just_pressed("ui_cancel"):
		open_ninja_dojo()

func execute_ninja_movement(direction: Vector2):
	var new_position = player_data.position + direction

	# Vérifier les limites du territoire ninja
	if new_position.x < 0 or new_position.x >= 100 or new_position.y < 0 or new_position.y >= 100:
		show_ninja_message("🚫 Limite du territoire ninja! Impossible d'aller plus loin.")
		return

	# Vérifier le chakra pour le mouvement
	if not use_ninja_chakra(config.movement_cost):
		show_ninja_message("💨 Chakra insuffisant pour ce déplacement ninja!")
		return

	# Exécuter le mouvement ninja
	is_moving = true
	var new_pixel_pos = new_position * 32

	# Animation de déplacement ninja
	var tween = create_tween()
	tween.tween_property(player_sprite, "position", new_pixel_pos, 0.12)
	await tween.finished

	# Mettre à jour la position
	var old_position = player_data.position
	player_data.position = new_position

	# Calculer la distance parcourue
	var distance = old_position.distance_to(new_position)
	game_stats.distance_traveled += distance

	# Marquer comme exploré
	explore_ninja_tile(new_position)

	# Chance de rencontre ninja
	if randf() < 0.15:  # 15% de chance
		trigger_ninja_encounter()

	# Mettre à jour l'interface
	update_ninja_interface()

	is_moving = false

func use_ninja_chakra(amount: int) -> bool:
	if player_data.chakra >= amount:
		player_data.chakra -= amount
		emit_signal("chakra_changed")
		return true
	return false

func execute_ninja_scan():
	if not use_ninja_chakra(config.scan_cost):
		show_ninja_message("💨 Chakra insuffisant pour la technique de scan ninja!")
		return

	# Technique de reconnaissance ninja
	var scan_radius = 4
	var player_tile = player_data.position

	for x in range(-scan_radius, scan_radius + 1):
		for y in range(-scan_radius, scan_radius + 1):
			var tile_pos = player_tile + Vector2(x, y)
			if tile_pos.x >= 0 and tile_pos.x < 100 and tile_pos.y >= 0 and tile_pos.y < 100:
				explore_ninja_tile(tile_pos)

	show_ninja_message("👁️ Technique de Scan Ninja activée! Zone révélée dans un rayon de " + str(scan_radius) + " tuiles.")

func execute_ninja_search():
	show_ninja_message("🔍 Fouille ninja en cours...")

	# Technique de fouille ninja
	if randf() < 0.3:  # 30% de chance
		var ninja_findings = [
			{"item": "chakra_pill", "name": "💊 Pilule de Chakra Ninja"},
			{"item": "health_potion", "name": "🧪 Élixir de Santé"},
			{"item": "ninja_star", "name": "⭐ Shuriken Authentique"},
			{"item": "smoke_bomb", "name": "💨 Bombe Fumigène Ninja"},
			{"item": "ninja_scroll", "name": "📜 Parchemin Ancien"}
		]

		var finding = ninja_findings[randi() % ninja_findings.size()]
		add_ninja_item(finding.item, 1)
		show_ninja_message("🎁 Découverte ninja: " + finding.name + "!")
	else:
		var search_messages = [
			"🍃 Seules des feuilles d'automne dansent dans le vent...",
			"🪨 Des pierres ordinaires, rien d'intéressant pour un ninja...",
			"🌸 Des pétales de cerisier tombent silencieusement...",
			"🦗 Le chant des grillons résonne dans la nuit ninja...",
			"🌙 L'ombre de la lune cache tous les secrets..."
		]
		show_ninja_message(search_messages[randi() % search_messages.size()])

func trigger_ninja_encounter():
	var ninja_enemies = [
		{"name": "🐗 Sanglier Sauvage", "xp": 30, "type": "beast", "difficulty": 1},
		{"name": "🐺 Loup Alpha", "xp": 45, "type": "beast", "difficulty": 2},
		{"name": "🥷 Ninja Renégat", "xp": 60, "type": "ninja", "difficulty": 3},
		{"name": "👹 Oni Mineur", "xp": 50, "type": "demon", "difficulty": 3},
		{"name": "🦅 Faucon Géant", "xp": 35, "type": "beast", "difficulty": 2},
		{"name": "🐍 Serpent Venimeux", "xp": 40, "type": "beast", "difficulty": 2},
		{"name": "👤 Assassin de l'Ombre", "xp": 70, "type": "ninja", "difficulty": 4}
	]

	var enemy = ninja_enemies[randi() % ninja_enemies.size()]

	show_ninja_message("⚔️ COMBAT NINJA! Ennemi détecté: " + enemy.name)

	# Système de combat ninja avancé
	var player_power = player_data.level + player_data.attack + player_data.speed
	var enemy_power = enemy.difficulty * 15
	var victory_chance = 0.6 + (player_power - enemy_power) * 0.02
	victory_chance = clamp(victory_chance, 0.1, 0.95)

	var is_victory = randf() < victory_chance

	if is_victory:
		# Victoire ninja
		var xp_gain = enemy.xp + randi() % 20
		gain_ninja_xp(xp_gain)
		game_stats.battles_won += 1
		game_stats.enemies_defeated += 1
		game_stats.total_damage_dealt += enemy.difficulty * 10

		show_ninja_message("🏆 Victoire ninja éclatante! +" + str(xp_gain) + " XP gagné!")

		# Loot ninja selon le type d'ennemi
		var loot_chance = randf()
		match enemy.type:
			"ninja":
				if loot_chance < 0.4:
					add_ninja_item("ninja_scroll", 1)
				elif loot_chance < 0.6:
					add_ninja_item("smoke_bomb", 1)
			"beast":
				if loot_chance < 0.3:
					add_ninja_item("chakra_pill", 1)
				elif loot_chance < 0.5:
					add_ninja_item("health_potion", 1)
			"demon":
				if loot_chance < 0.2:
					add_ninja_item("rare_gem", 1)
				elif loot_chance < 0.4:
					add_ninja_item("ninja_scroll", 1)
	else:
		# Défaite ninja - mais pas de mort
		var damage = 15 + randi() % 15
		player_data.hp = max(5, player_data.hp - damage)  # Minimum 5 HP
		show_ninja_message("💥 Défaite temporaire... -" + str(damage) + " HP. Un ninja ne renonce jamais!")
		emit_signal("player_stats_changed")

func explore_ninja_tile(tile_pos: Vector2):
	var key = str(tile_pos.x) + "," + str(tile_pos.y)
	explored_tiles[key] = true

func is_ninja_tile_explored(tile_pos: Vector2) -> bool:
	var key = str(tile_pos.x) + "," + str(tile_pos.y)
	return explored_tiles.has(key)

# === SYSTÈME DE PROGRESSION NINJA ===

func gain_ninja_xp(amount: int):
	player_data.xp += amount
	print("⭐ XP ninja gagné: ", amount, " (Total: ", player_data.xp, ")")
	check_ninja_level_up()
	emit_signal("player_stats_changed")

func check_ninja_level_up():
	var xp_needed = get_xp_for_level(player_data.level + 1)

	while player_data.xp >= xp_needed and player_data.level < config.max_level:
		execute_ninja_level_up()
		xp_needed = get_xp_for_level(player_data.level + 1)

func execute_ninja_level_up():
	player_data.level += 1

	# Augmentation des stats ninja
	var hp_increase = 20 + randi() % 15
	var chakra_increase = 4 + randi() % 6
	var attack_increase = 2 + randi() % 4
	var defense_increase = 1 + randi() % 4
	var speed_increase = 1 + randi() % 3
	var stealth_increase = 1 + randi() % 2

	player_data.max_hp += hp_increase
	player_data.hp = player_data.max_hp  # Guérison complète
	player_data.max_chakra += chakra_increase
	player_data.chakra = player_data.max_chakra
	player_data.attack += attack_increase
	player_data.defense += defense_increase
	player_data.speed += speed_increase
	player_data.stealth += stealth_increase

	print("🎉 ASCENSION NINJA! Niveau ", player_data.level, " atteint!")
	print("  ❤️ HP: +", hp_increase, " (", player_data.max_hp, ")")
	print("  🔵 Chakra: +", chakra_increase, " (", player_data.max_chakra, ")")
	print("  ⚔️ Attaque: +", attack_increase, " (", player_data.attack, ")")

	emit_signal("level_up_achieved", player_data.level)
	emit_signal("player_stats_changed")
	emit_signal("chakra_changed")

func get_xp_for_level(level: int) -> int:
	return int(config.base_xp * pow(config.xp_multiplier, level - 1))

func get_xp_for_current_level() -> int:
	var current_level_xp = get_xp_for_level(player_data.level)
	return player_data.xp - current_level_xp

func get_xp_for_next_level() -> int:
	var current_level_xp = get_xp_for_level(player_data.level)
	var next_level_xp = get_xp_for_level(player_data.level + 1)
	return next_level_xp - current_level_xp

func add_ninja_item(item_id: String, quantity: int = 1):
	if inventory.has(item_id):
		inventory[item_id] += quantity
	else:
		inventory[item_id] = quantity

	print("🎒 Objet ninja ajouté: ", item_id, " x", quantity)
	emit_signal("item_obtained", item_id, quantity)

func use_ninja_item(item_id: String) -> bool:
	if not inventory.has(item_id) or inventory[item_id] <= 0:
		return false

	inventory[item_id] -= 1
	if inventory[item_id] <= 0:
		inventory.erase(item_id)

	apply_ninja_item_effect(item_id)
	return true

func apply_ninja_item_effect(item_id: String):
	match item_id:
		"chakra_pill":
			player_data.chakra = min(player_data.chakra + 15, player_data.max_chakra)
			show_ninja_message("💊 Pilule de chakra ninja consommée: +15 chakra!")
			emit_signal("chakra_changed")
		"health_potion":
			player_data.hp = min(player_data.hp + 40, player_data.max_hp)
			show_ninja_message("🧪 Élixir de santé ninja bu: +40 HP!")
			emit_signal("player_stats_changed")
		"ninja_scroll":
			# Bonus temporaire ou permanent selon le scroll
			if randf() < 0.5:
				player_data.attack += 1
				show_ninja_message("📜 Parchemin ninja étudié: +1 Attaque permanente!")
			else:
				gain_ninja_xp(50)
				show_ninja_message("📜 Parchemin ninja déchiffré: +50 XP!")

# === INTERFACE ET MENUS NINJA ===

func show_ninja_message(text: String):
	print("🥷 Message Ninja: ", text)
	if message_label:
		message_label.text = text
		message_label.visible = true
		message_timer.start()

func _on_ninja_message_timeout():
	if message_label:
		message_label.visible = false

func update_ninja_interface():
	if position_label:
		position_label.text = "📍 Territoire: " + str(player_data.position)

func open_ninja_dojo():
	var dojo_menu = AcceptDialog.new()
	dojo_menu.title = "🏮 DOJO DU MAÎTRE NINJA 🏮"
	dojo_menu.size = Vector2(500, 600)

	var scroll = ScrollContainer.new()
	dojo_menu.add_child(scroll)

	var vbox = VBoxContainer.new()
	scroll.add_child(vbox)

	# Statistiques ninja complètes
	var ninja_stats = create_ninja_stats_text()
	var dojo_stats_label = Label.new()
	dojo_stats_label.text = ninja_stats
	dojo_stats_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	vbox.add_child(dojo_stats_label)

	var separator = HSeparator.new()
	vbox.add_child(separator)

	# Actions du dojo ninja
	var continue_btn = Button.new()
	continue_btn.text = "🏃 Continuer la Mission Ninja"
	continue_btn.pressed.connect(dojo_menu.queue_free)
	vbox.add_child(continue_btn)

	var training_btn = Button.new()
	training_btn.text = "🥋 Entraînement Intensif (100 XP)"
	training_btn.pressed.connect(_on_ninja_training.bind(dojo_menu))
	vbox.add_child(training_btn)

	var meditation_btn = Button.new()
	meditation_btn.text = "🧘 Méditation Ninja (Restaurer Tout)"
	meditation_btn.pressed.connect(_on_ninja_meditation.bind(dojo_menu))
	vbox.add_child(meditation_btn)

	var items_btn = Button.new()
	items_btn.text = "🎒 Utiliser Objets Ninja"
	items_btn.pressed.connect(_on_use_ninja_items.bind(dojo_menu))
	vbox.add_child(items_btn)

	var quit_btn = Button.new()
	quit_btn.text = "🚪 Quitter le Dojo"
	quit_btn.pressed.connect(get_tree().quit)
	vbox.add_child(quit_btn)

	add_child(dojo_menu)
	dojo_menu.popup_centered()

func create_ninja_stats_text() -> String:
	var stats = "🥷 " + player_data.name + " du " + clans[player_data.clan].name + "\n\n"
	stats += "📊 STATISTIQUES DU NINJA:\n"
	stats += "Niveau: " + str(player_data.level) + " / " + str(config.max_level) + "\n"
	stats += "XP: " + str(player_data.xp) + " / " + str(get_xp_for_level(player_data.level + 1)) + "\n"
	stats += "HP: " + str(player_data.hp) + " / " + str(player_data.max_hp) + "\n"
	stats += "Chakra: " + str(player_data.chakra) + " / " + str(player_data.max_chakra) + "\n"
	stats += "Attaque: " + str(player_data.attack) + "\n"
	stats += "Défense: " + str(player_data.defense) + "\n"
	stats += "Vitesse: " + str(player_data.speed) + "\n"
	stats += "Furtivité: " + str(player_data.stealth) + "\n\n"

	stats += "⚔️ EXPLOITS NINJA:\n"
	stats += "Combats gagnés: " + str(game_stats.battles_won) + "\n"
	stats += "Ennemis vaincus: " + str(game_stats.enemies_defeated) + "\n"
	stats += "Dégâts infligés: " + str(game_stats.total_damage_dealt) + "\n"
	stats += "Distance parcourue: " + str(int(game_stats.distance_traveled)) + " pas ninja\n"
	stats += "Temps d'entraînement: " + str(int(game_stats.play_time)) + " secondes\n\n"

	if not inventory.is_empty():
		stats += "🎒 ARSENAL NINJA:\n"
		for item_id in inventory.keys():
			var item_name = get_ninja_item_name(item_id)
			stats += "  " + item_name + " x" + str(inventory[item_id]) + "\n"
	else:
		stats += "🎒 Arsenal ninja vide\n"

	return stats

func get_ninja_item_name(item_id: String) -> String:
	match item_id:
		"chakra_pill": return "💊 Pilule de Chakra"
		"health_potion": return "🧪 Élixir de Santé"
		"ninja_star": return "⭐ Shuriken"
		"smoke_bomb": return "💨 Bombe Fumigène"
		"ninja_scroll": return "📜 Parchemin Ninja"
		"rare_gem": return "💎 Gemme Rare"
		_: return "❓ " + item_id

# === ACTIONS DU DOJO NINJA ===

func _on_ninja_training(menu: AcceptDialog):
	show_ninja_message("🥋 Entraînement ninja intensif accompli!")
	gain_ninja_xp(100)

	# Chance d'améliorer les stats
	if randf() < 0.4:
		var stat_boost = randi() % 3
		match stat_boost:
			0:
				player_data.attack += 1
				show_ninja_message("💪 Votre technique d'attaque s'améliore! +1 Attaque")
			1:
				player_data.defense += 1
				show_ninja_message("🛡️ Votre défense se renforce! +1 Défense")
			2:
				player_data.speed += 1
				show_ninja_message("⚡ Votre vitesse augmente! +1 Vitesse")

		emit_signal("player_stats_changed")

	menu.queue_free()

func _on_ninja_meditation(menu: AcceptDialog):
	player_data.hp = player_data.max_hp
	player_data.chakra = player_data.max_chakra
	show_ninja_message("🧘 Méditation ninja accomplie. Corps et esprit restaurés!")
	update_all_ninja_ui()
	menu.queue_free()

func _on_use_ninja_items(menu: AcceptDialog):
	if inventory.is_empty():
		show_ninja_message("🎒 Votre arsenal ninja est vide!")
		return

	# Créer un menu d'objets
	var items_menu = AcceptDialog.new()
	items_menu.title = "🎒 Arsenal Ninja"
	items_menu.size = Vector2(300, 400)

	var vbox = VBoxContainer.new()
	items_menu.add_child(vbox)

	for item_id in inventory.keys():
		var item_btn = Button.new()
		var item_name = get_ninja_item_name(item_id)
		item_btn.text = item_name + " x" + str(inventory[item_id])
		item_btn.pressed.connect(_on_use_specific_item.bind(item_id, items_menu))
		vbox.add_child(item_btn)

	add_child(items_menu)
	items_menu.popup_centered()
	menu.queue_free()

func _on_use_specific_item(item_id: String, menu: AcceptDialog):
	if use_ninja_item(item_id):
		show_ninja_message("✨ Objet ninja utilisé avec succès!")
		update_all_ninja_ui()
	else:
		show_ninja_message("❌ Impossible d'utiliser cet objet ninja!")
	menu.queue_free()

# === CALLBACKS DES SIGNAUX NINJA ===

func _on_ninja_stats_changed():
	update_all_ninja_ui()

func _on_ninja_chakra_changed():
	update_all_ninja_ui()

func _on_ninja_level_up(new_level: int):
	show_ninja_message("🎉 ASCENSION NINJA ACCOMPLIE! Niveau " + str(new_level) + " atteint! Nouvelles techniques débloquées!")
	update_all_ninja_ui()

func _on_ninja_item_obtained(item_id: String, quantity: int):
	var item_name = get_ninja_item_name(item_id)
	show_ninja_message("🎁 " + item_name + " x" + str(quantity) + " ajouté à votre arsenal ninja!")

# === MISE À JOUR DE L'INTERFACE NINJA ===

func update_all_ninja_ui():
	# Mettre à jour les barres de stats
	if hp_bar:
		hp_bar.max_value = player_data.max_hp
		hp_bar.value = player_data.hp
		var hp_container = hp_bar.get_parent()
		if hp_container.get_child_count() > 2:
			hp_container.get_child(2).text = str(player_data.hp) + "/" + str(player_data.max_hp)

	if chakra_bar:
		chakra_bar.max_value = player_data.max_chakra
		chakra_bar.value = player_data.chakra
		var chakra_container = chakra_bar.get_parent()
		if chakra_container.get_child_count() > 2:
			chakra_container.get_child(2).text = str(player_data.chakra) + "/" + str(player_data.max_chakra)

	if xp_bar:
		var current_xp = get_xp_for_current_level()
		var needed_xp = get_xp_for_next_level()
		xp_bar.max_value = needed_xp
		xp_bar.value = current_xp
		var xp_container = xp_bar.get_parent()
		if xp_container.get_child_count() > 2:
			xp_container.get_child(2).text = str(current_xp) + "/" + str(needed_xp)

	if level_label:
		level_label.text = "忍者 Niv." + str(player_data.level)

	update_ninja_stats_display()
	update_ninja_interface()

# === FONCTIONS UTILITAIRES NINJA ===

func get_ninja_summary() -> String:
	var summary = "🥷 " + player_data.name + " du " + clans[player_data.clan].name + "\n"
	summary += "Niveau " + str(player_data.level) + " | "
	summary += "HP: " + str(player_data.hp) + "/" + str(player_data.max_hp) + " | "
	summary += "Chakra: " + str(player_data.chakra) + "/" + str(player_data.max_chakra) + "\n"
	summary += "Position: " + str(player_data.position) + " | "
	summary += "Combats gagnés: " + str(game_stats.battles_won)
	return summary

func save_ninja_progress() -> Dictionary:
	return {
		"player_data": player_data,
		"inventory": inventory,
		"learned_jutsu": learned_jutsu,
		"unlocked_regions": unlocked_regions,
		"explored_tiles": explored_tiles,
		"game_stats": game_stats,
		"version": "1.0.0",
		"save_timestamp": Time.get_unix_time_from_system()
	}

func load_ninja_progress(data: Dictionary):
	if data.has("player_data"):
		player_data = data.player_data
	if data.has("inventory"):
		inventory = data.inventory
	if data.has("learned_jutsu"):
		learned_jutsu = data.learned_jutsu
	if data.has("unlocked_regions"):
		unlocked_regions = data.unlocked_regions
	if data.has("explored_tiles"):
		explored_tiles = data.explored_tiles
	if data.has("game_stats"):
		game_stats = data.game_stats

	print("🥷 Progression ninja chargée avec succès!")
	update_ninja_position()
	update_all_ninja_ui()

# === MESSAGE DE FIN ===
# 🥷 Shinobi Chronicles - Version Complète Autonome
# Tous les systèmes ninja intégrés et fonctionnels!
# Que l'aventure ninja commence! ⚔️🌸