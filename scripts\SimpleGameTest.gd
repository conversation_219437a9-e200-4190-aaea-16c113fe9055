extends Node

# Version simplifiée sans dépendances pour tester le jeu
class_name SimpleGameTest

var player_position: Vector2 = Vector2(50, 50)
var player_hp: int = 100
var player_max_hp: int = 100
var player_chakra: int = 20
var player_max_chakra: int = 20
var player_level: int = 1
var player_xp: int = 0

var player_sprite: Sprite2D
var camera: Camera2D
var hud_label: Label

var is_moving: bool = false
var chakra_regen_timer: float = 0.0

func _ready():
	print("🥷 Shinobi Chronicles - Version Test Simple")
	setup_game()

func setup_game():
	# Créer le joueur
	setup_player()
	
	# Créer l'interface
	setup_hud()
	
	# Configurer les inputs
	set_process_input(true)
	set_process(true)
	
	print("Jeu initialisé ! Utilisez WASD pour bouger, F pour scanner.")
	print("HP: ", player_hp, "/", player_max_hp)
	print("Chakra: ", player_chakra, "/", player_max_chakra)

func setup_player():
	# C<PERSON>er le sprite du joueur
	player_sprite = Sprite2D.new()
	player_sprite.name = "Player"
	
	# Créer une texture simple (carré rouge)
	var image = Image.create(32, 32, false, Image.FORMAT_RGB8)
	image.fill(Color.RED)
	var texture = ImageTexture.new()
	texture.set_image(image)
	player_sprite.texture = texture
	
	add_child(player_sprite)
	
	# Position initiale
	var pixel_pos = player_position * 32  # Taille de tuile
	player_sprite.position = pixel_pos
	
	# Créer la caméra
	camera = Camera2D.new()
	camera.name = "Camera"
	camera.enabled = true
	player_sprite.add_child(camera)

func setup_hud():
	# Créer un label pour afficher les infos
	hud_label = Label.new()
	hud_label.name = "HUD"
	hud_label.position = Vector2(10, 10)
	hud_label.size = Vector2(300, 100)
	hud_label.add_theme_color_override("font_color", Color.WHITE)
	add_child(hud_label)
	
	update_hud()

func update_hud():
	if hud_label:
		hud_label.text = "🥷 Shinobi Chronicles\n"
		hud_label.text += "Niveau: " + str(player_level) + " | XP: " + str(player_xp) + "\n"
		hud_label.text += "HP: " + str(player_hp) + "/" + str(player_max_hp) + "\n"
		hud_label.text += "Chakra: " + str(player_chakra) + "/" + str(player_max_chakra) + "\n"
		hud_label.text += "Position: " + str(player_position)

func _process(delta):
	# Régénération de chakra (plus rapide pour les tests)
	chakra_regen_timer += delta
	if chakra_regen_timer >= 5.0:  # 5 secondes au lieu de 60
		if player_chakra < player_max_chakra:
			player_chakra += 1
			update_hud()
		chakra_regen_timer = 0.0

func _input(event):
	if is_moving:
		return
	
	var movement_vector = Vector2.ZERO
	
	# Gestion des mouvements
	if Input.is_action_pressed("move_up"):
		movement_vector.y = -1
	elif Input.is_action_pressed("move_down"):
		movement_vector.y = 1
	elif Input.is_action_pressed("move_left"):
		movement_vector.x = -1
	elif Input.is_action_pressed("move_right"):
		movement_vector.x = 1
	
	if movement_vector != Vector2.ZERO:
		move_player(movement_vector)
	
	# Actions spéciales
	if Input.is_action_just_pressed("scan"):
		scan_area()
	elif Input.is_action_just_pressed("interact"):
		interact()
	elif Input.is_action_just_pressed("ui_cancel"):
		show_pause_menu()

func move_player(direction: Vector2):
	var new_world_pos = player_position + direction
	
	# Vérifier les limites du monde (100x100)
	if new_world_pos.x < 0 or new_world_pos.x >= 100:
		return
	if new_world_pos.y < 0 or new_world_pos.y >= 100:
		return
	
	# Vérifier si le joueur a assez de chakra
	if player_chakra < 1:
		show_message("Pas assez de chakra pour bouger!")
		return
	
	# Utiliser du chakra
	player_chakra -= 1
	
	# Déplacer le joueur
	is_moving = true
	var new_pixel_pos = new_world_pos * 32
	
	# Animation de mouvement
	var tween = create_tween()
	tween.tween_property(player_sprite, "position", new_pixel_pos, 0.2)
	await tween.finished
	
	# Mettre à jour la position
	player_position = new_world_pos
	
	# Chance de rencontre (réduite pour les tests)
	if randf() < 0.05:  # 5% de chance
		trigger_encounter()
	
	# Mettre à jour l'interface
	update_hud()
	
	is_moving = false

func scan_area():
	if player_chakra < 5:
		show_message("Pas assez de chakra pour scanner!")
		return
	
	# Utiliser du chakra
	player_chakra -= 5
	
	show_message("Zone scannée! (Rayon: 3 tuiles)")
	update_hud()

func trigger_encounter():
	show_message("Rencontre avec un ennemi! (Combat simplifié)")
	
	# Combat simplifié - juste gagner de l'XP
	player_xp += 25
	
	# Vérifier montée de niveau
	var xp_needed = 100 * player_level  # XP simple
	if player_xp >= xp_needed:
		level_up()
	
	update_hud()

func level_up():
	player_level += 1
	player_xp = 0
	
	# Augmenter les stats
	player_max_hp += 15
	player_hp = player_max_hp  # Heal complet
	player_max_chakra += 3
	player_chakra = player_max_chakra
	
	show_message("Niveau supérieur! Nouveau niveau: " + str(player_level))
	update_hud()

func interact():
	show_message("Rien à interagir ici.")

func show_message(text: String):
	print("Message: ", text)
	
	# Créer un label temporaire pour le message
	var message_label = Label.new()
	message_label.text = text
	message_label.position = Vector2(10, 150)
	message_label.add_theme_color_override("font_color", Color.YELLOW)
	add_child(message_label)
	
	# Faire disparaître le message après 3 secondes
	var timer = Timer.new()
	timer.wait_time = 3.0
	timer.one_shot = true
	timer.timeout.connect(func(): message_label.queue_free(); timer.queue_free())
	add_child(timer)
	timer.start()

func show_pause_menu():
	var pause_menu = AcceptDialog.new()
	pause_menu.title = "Menu de Pause - Shinobi Chronicles"
	pause_menu.size = Vector2(350, 300)
	
	var vbox = VBoxContainer.new()
	pause_menu.add_child(vbox)
	
	# Informations du joueur
	var info_label = Label.new()
	info_label.text = "🥷 Statistiques du Ninja:\n\n"
	info_label.text += "Niveau: " + str(player_level) + "\n"
	info_label.text += "XP: " + str(player_xp) + "/" + str(100 * player_level) + "\n"
	info_label.text += "HP: " + str(player_hp) + "/" + str(player_max_hp) + "\n"
	info_label.text += "Chakra: " + str(player_chakra) + "/" + str(player_max_chakra) + "\n"
	info_label.text += "Position: " + str(player_position) + "\n\n"
	info_label.text += "Contrôles:\n"
	info_label.text += "WASD: Bouger (1 chakra)\n"
	info_label.text += "F: Scanner (5 chakra)\n"
	info_label.text += "E: Interagir\n"
	info_label.text += "Échap: Menu"
	vbox.add_child(info_label)
	
	var separator = HSeparator.new()
	vbox.add_child(separator)
	
	# Bouton Continuer
	var continue_button = Button.new()
	continue_button.text = "Continuer l'Aventure"
	continue_button.pressed.connect(pause_menu.queue_free)
	vbox.add_child(continue_button)
	
	# Bouton Test Combat
	var combat_button = Button.new()
	combat_button.text = "Déclencher Combat"
	combat_button.pressed.connect(_on_test_combat.bind(pause_menu))
	vbox.add_child(combat_button)
	
	# Bouton Gagner XP
	var xp_button = Button.new()
	xp_button.text = "Gagner 50 XP"
	xp_button.pressed.connect(_on_gain_xp.bind(pause_menu))
	vbox.add_child(xp_button)
	
	# Bouton Restaurer Chakra
	var chakra_button = Button.new()
	chakra_button.text = "Restaurer Chakra"
	chakra_button.pressed.connect(_on_restore_chakra.bind(pause_menu))
	vbox.add_child(chakra_button)
	
	# Bouton Quitter
	var quit_button = Button.new()
	quit_button.text = "Quitter le Jeu"
	quit_button.pressed.connect(get_tree().quit)
	vbox.add_child(quit_button)
	
	add_child(pause_menu)
	pause_menu.popup_centered()

func _on_test_combat(menu: AcceptDialog):
	show_message("Combat victorieux! Vous gagnez de l'XP!")
	player_xp += 50
	
	# Vérifier montée de niveau
	var xp_needed = 100 * player_level
	if player_xp >= xp_needed:
		level_up()
	
	update_hud()
	menu.queue_free()

func _on_gain_xp(menu: AcceptDialog):
	player_xp += 50
	show_message("50 XP gagnés!")
	
	# Vérifier montée de niveau
	var xp_needed = 100 * player_level
	if player_xp >= xp_needed:
		level_up()
	
	update_hud()
	menu.queue_free()

func _on_restore_chakra(menu: AcceptDialog):
	player_chakra = player_max_chakra
	show_message("Chakra restauré!")
	update_hud()
	menu.queue_free()
