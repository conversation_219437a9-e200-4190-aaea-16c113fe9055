extends Control

# Interface utilisateur principale pour Shinobi Chronicles
class_name HUD

@onready var player_info_panel: Panel
@onready var minimap_panel: Panel
@onready var message_panel: Panel
@onready var controls_panel: Panel

# Éléments d'information du joueur
var player_name_label: Label
var level_label: Label
var hp_bar: ProgressBar
var hp_label: Label
var chakra_bar: ProgressBar
var chakra_label: Label
var xp_bar: ProgressBar
var xp_label: Label
var clan_label: Label
var region_label: Label

# Minimap
var minimap_texture: TextureRect
var player_dot: ColorRect

# Messages
var message_label: Label
var message_timer: Timer

# Contrôles
var controls_label: Label

var game_state: GameState

signal hud_ready

func _ready():
	setup_hud()
	emit_signal("hud_ready")

func setup_hud():
	# Configuration de base
	set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	mouse_filter = Control.MOUSE_FILTER_IGNORE
	
	# Panel d'informations du joueur (coin supérieur gauche)
	create_player_info_panel()
	
	# Minimap (coin supérieur droit)
	create_minimap_panel()
	
	# Panel de messages (centre bas)
	create_message_panel()
	
	# Panel de contrôles (coin inférieur droit)
	create_controls_panel()

func create_player_info_panel():
	player_info_panel = Panel.new()
	player_info_panel.position = Vector2(10, 10)
	player_info_panel.size = Vector2(250, 180)
	add_child(player_info_panel)
	
	var vbox = VBoxContainer.new()
	vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	vbox.add_theme_constant_override("separation", 5)
	player_info_panel.add_child(vbox)
	
	# Nom et niveau
	var name_hbox = HBoxContainer.new()
	vbox.add_child(name_hbox)
	
	player_name_label = Label.new()
	player_name_label.text = "Ninja"
	player_name_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	name_hbox.add_child(player_name_label)
	
	level_label = Label.new()
	level_label.text = "Niv. 1"
	level_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
	name_hbox.add_child(level_label)
	
	# HP
	var hp_hbox = HBoxContainer.new()
	vbox.add_child(hp_hbox)
	
	var hp_icon = Label.new()
	hp_icon.text = "❤️"
	hp_hbox.add_child(hp_icon)
	
	hp_bar = ProgressBar.new()
	hp_bar.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	hp_bar.show_percentage = false
	hp_hbox.add_child(hp_bar)
	
	hp_label = Label.new()
	hp_label.text = "100/100"
	hp_hbox.add_child(hp_label)
	
	# Chakra
	var chakra_hbox = HBoxContainer.new()
	vbox.add_child(chakra_hbox)
	
	var chakra_icon = Label.new()
	chakra_icon.text = "🔵"
	chakra_hbox.add_child(chakra_icon)
	
	chakra_bar = ProgressBar.new()
	chakra_bar.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	chakra_bar.show_percentage = false
	chakra_bar.modulate = Color.CYAN
	chakra_hbox.add_child(chakra_bar)
	
	chakra_label = Label.new()
	chakra_label.text = "20/20"
	chakra_hbox.add_child(chakra_label)
	
	# XP
	var xp_hbox = HBoxContainer.new()
	vbox.add_child(xp_hbox)
	
	var xp_icon = Label.new()
	xp_icon.text = "⭐"
	xp_hbox.add_child(xp_icon)
	
	xp_bar = ProgressBar.new()
	xp_bar.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	xp_bar.show_percentage = false
	xp_bar.modulate = Color.YELLOW
	xp_hbox.add_child(xp_bar)
	
	xp_label = Label.new()
	xp_label.text = "0/100"
	xp_hbox.add_child(xp_label)
	
	# Clan
	clan_label = Label.new()
	clan_label.text = "Clan: Ryūjin"
	clan_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(clan_label)
	
	# Région
	region_label = Label.new()
	region_label.text = "Région: Village de Départ"
	region_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(region_label)

func create_minimap_panel():
	minimap_panel = Panel.new()
	minimap_panel.position = Vector2(get_viewport().size.x - 160, 10)
	minimap_panel.size = Vector2(150, 150)
	minimap_panel.anchor_left = 1.0
	minimap_panel.anchor_right = 1.0
	minimap_panel.offset_left = -160
	minimap_panel.offset_right = -10
	add_child(minimap_panel)
	
	var minimap_label = Label.new()
	minimap_label.text = "Minimap"
	minimap_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	minimap_label.position = Vector2(0, 5)
	minimap_label.size = Vector2(150, 20)
	minimap_panel.add_child(minimap_label)
	
	# Zone de la minimap
	minimap_texture = TextureRect.new()
	minimap_texture.position = Vector2(10, 25)
	minimap_texture.size = Vector2(130, 110)
	minimap_texture.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
	minimap_panel.add_child(minimap_texture)
	
	# Point du joueur sur la minimap
	player_dot = ColorRect.new()
	player_dot.color = Color.RED
	player_dot.size = Vector2(4, 4)
	player_dot.position = Vector2(63, 63)  # Centre de la minimap
	minimap_texture.add_child(player_dot)
	
	# Créer une texture simple pour la minimap
	create_simple_minimap()

func create_simple_minimap():
	# Créer une texture simple pour la minimap
	var image = Image.create(64, 64, false, Image.FORMAT_RGB8)
	
	# Remplir avec différentes couleurs selon les régions
	for x in range(64):
		for y in range(64):
			var color = Color.GREEN  # Couleur par défaut
			
			# Différentes régions avec différentes couleurs
			if x < 16 and y < 16:
				color = Color.BROWN  # Village
			elif x < 32 and y < 32:
				color = Color.DARK_GREEN  # Forêt
			elif x < 48 and y < 48:
				color = Color(0.2, 0.4, 0.2)  # Bois sombres
			else:
				color = Color.GRAY  # Montagnes
			
			image.set_pixel(x, y, color)
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	minimap_texture.texture = texture

func create_message_panel():
	message_panel = Panel.new()
	message_panel.anchor_left = 0.5
	message_panel.anchor_right = 0.5
	message_panel.anchor_top = 1.0
	message_panel.anchor_bottom = 1.0
	message_panel.offset_left = -200
	message_panel.offset_right = 200
	message_panel.offset_top = -80
	message_panel.offset_bottom = -10
	message_panel.modulate.a = 0.8
	add_child(message_panel)
	
	message_label = Label.new()
	message_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	message_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	message_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	message_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	message_panel.add_child(message_label)
	
	# Timer pour faire disparaître les messages
	message_timer = Timer.new()
	message_timer.wait_time = 3.0
	message_timer.one_shot = true
	message_timer.timeout.connect(_on_message_timer_timeout)
	add_child(message_timer)
	
	# Cacher le panel au début
	message_panel.visible = false

func create_controls_panel():
	controls_panel = Panel.new()
	controls_panel.anchor_left = 1.0
	controls_panel.anchor_right = 1.0
	controls_panel.anchor_top = 1.0
	controls_panel.anchor_bottom = 1.0
	controls_panel.offset_left = -200
	controls_panel.offset_right = -10
	controls_panel.offset_top = -120
	controls_panel.offset_bottom = -10
	controls_panel.modulate.a = 0.7
	add_child(controls_panel)
	
	controls_label = Label.new()
	controls_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	controls_label.text = "Contrôles:\nWASD/Flèches: Bouger\nF: Scanner\nE/Espace: Interagir\nI: Inventaire\nS: Compétences\nM: Carte"
	controls_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	controls_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	controls_panel.add_child(controls_label)

func initialize(player_state: GameState):
	game_state = player_state
	
	# Connecter les signaux
	game_state.player_stats_changed.connect(_on_player_stats_changed)
	game_state.chakra_changed.connect(_on_chakra_changed)
	game_state.level_up.connect(_on_level_up)
	game_state.region_unlocked.connect(_on_region_unlocked)
	
	# Mettre à jour l'interface initiale
	update_player_info()

func update_player_info():
	if not game_state:
		return
	
	var player = game_state.player_data
	
	# Nom et niveau
	player_name_label.text = player.name
	level_label.text = "Niv. " + str(player.level)
	
	# HP
	hp_bar.max_value = player.max_hp
	hp_bar.value = player.hp
	hp_label.text = str(player.hp) + "/" + str(player.max_hp)
	
	# Chakra
	chakra_bar.max_value = player.max_chakra
	chakra_bar.value = player.chakra
	chakra_label.text = str(player.chakra) + "/" + str(player.max_chakra)
	
	# XP
	var current_xp = player.xp
	var xp_for_current_level = GameConfig.get_xp_for_level(player.level)
	var xp_for_next_level = GameConfig.get_xp_for_level(player.level + 1)
	var xp_progress = current_xp - xp_for_current_level
	var xp_needed = xp_for_next_level - xp_for_current_level
	
	xp_bar.max_value = xp_needed
	xp_bar.value = xp_progress
	xp_label.text = str(xp_progress) + "/" + str(xp_needed)
	
	# Clan
	var clan_data = GameConfig.get_clan_data(player.clan)
	clan_label.text = "Clan: " + clan_data.get("name", "Inconnu")
	
	# Région
	var region_data = GameConfig.get_region_data(player.current_region)
	region_label.text = "Région: " + region_data.get("name", "Inconnue")

func update_minimap_position():
	if not game_state:
		return
	
	var player_pos = game_state.player_data.position
	var map_size = Vector2(GameConfig.WORLD_CONFIG.map_width, GameConfig.WORLD_CONFIG.map_height)
	
	# Calculer la position relative sur la minimap
	var minimap_size = minimap_texture.size
	var relative_pos = Vector2(
		(player_pos.x / map_size.x) * minimap_size.x,
		(player_pos.y / map_size.y) * minimap_size.y
	)
	
	player_dot.position = relative_pos - player_dot.size / 2

func show_message(text: String, duration: float = 3.0):
	message_label.text = text
	message_panel.visible = true
	message_timer.wait_time = duration
	message_timer.start()

func _on_message_timer_timeout():
	message_panel.visible = false

# Callbacks des signaux
func _on_player_stats_changed():
	update_player_info()
	update_minimap_position()

func _on_chakra_changed():
	update_player_info()

func _on_level_up(new_level: int):
	show_message("Niveau supérieur! Nouveau niveau: " + str(new_level), 4.0)
	update_player_info()

func _on_region_unlocked(region_id: String):
	var region_data = GameConfig.get_region_data(region_id)
	show_message("Nouvelle région débloquée: " + region_data.get("name", region_id), 4.0)
