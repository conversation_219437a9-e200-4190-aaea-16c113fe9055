extends Control

# Menu principal pour Shinobi Chronicles - Style Mobile

var background: TextureRect
var title_label: Label
var subtitle_label: Label
var buttons_container: VBoxContainer
var clan_selection_panel: Panel
var settings_panel: Panel

# Données de jeu
var selected_clan = "ryujin"
var player_name = "Ninja"

signal start_game(clan: String, name: String)
signal quit_game

func _ready():
	print("🥷 Shinobi Chronicles - Menu Principal")
	setup_mobile_menu()

func setup_mobile_menu():
	# Configuration pour mobile
	set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	# Arrière-plan dégradé ninja
	create_ninja_background()
	
	# Titre principal
	create_title()
	
	# Menu principal
	create_main_buttons()
	
	# Panels cachés
	create_clan_selection()
	create_settings_panel()

func create_ninja_background():
	# Arrière-plan avec dégradé ninja
	background = TextureRect.new()
	background.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	# Créer un dégradé ninja
	var gradient = Gradient.new()
	gradient.add_point(0.0, Color(0.1, 0.05, 0.2))  # Violet foncé
	gradient.add_point(0.5, Color(0.2, 0.1, 0.3))   # Violet moyen
	gradient.add_point(1.0, Color(0.05, 0.05, 0.15)) # Bleu très foncé
	
	var gradient_texture = GradientTexture2D.new()
	gradient_texture.gradient = gradient
	gradient_texture.fill_from = Vector2(0, 0)
	gradient_texture.fill_to = Vector2(0, 1)
	
	background.texture = gradient_texture
	add_child(background)

func create_title():
	# Container pour le titre
	var title_container = VBoxContainer.new()
	title_container.set_anchors_and_offsets_preset(Control.PRESET_CENTER_TOP)
	title_container.position.y = 80
	title_container.size = Vector2(400, 150)
	add_child(title_container)
	
	# Titre principal
	title_label = Label.new()
	title_label.text = "🥷 SHINOBI CHRONICLES 🥷"
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 32)
	title_label.add_theme_color_override("font_color", Color.GOLD)
	title_label.add_theme_color_override("font_shadow_color", Color.BLACK)
	title_label.add_theme_constant_override("shadow_offset_x", 3)
	title_label.add_theme_constant_override("shadow_offset_y", 3)
	title_container.add_child(title_label)
	
	# Sous-titre
	subtitle_label = Label.new()
	subtitle_label.text = "⚔️ L'Art du Ninja ⚔️"
	subtitle_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	subtitle_label.add_theme_font_size_override("font_size", 18)
	subtitle_label.add_theme_color_override("font_color", Color.CYAN)
	title_container.add_child(subtitle_label)
	
	# Animation du titre
	animate_title()

func animate_title():
	# Animation de pulsation pour le titre
	var tween = create_tween()
	tween.set_loops()
	tween.tween_property(title_label, "modulate:a", 0.7, 1.5)
	tween.tween_property(title_label, "modulate:a", 1.0, 1.5)

func create_main_buttons():
	# Container pour les boutons
	buttons_container = VBoxContainer.new()
	buttons_container.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	buttons_container.position.y = 50
	buttons_container.add_theme_constant_override("separation", 20)
	add_child(buttons_container)
	
	# Bouton Nouvelle Partie
	var new_game_btn = create_ninja_button("🆕 NOUVELLE PARTIE", Color.GREEN)
	new_game_btn.pressed.connect(_on_new_game_pressed)
	buttons_container.add_child(new_game_btn)
	
	# Bouton Continuer (si sauvegarde existe)
	var continue_btn = create_ninja_button("▶️ CONTINUER", Color.BLUE)
	continue_btn.pressed.connect(_on_continue_pressed)
	continue_btn.disabled = not has_save_file()
	buttons_container.add_child(continue_btn)
	
	# Bouton Sélection Clan
	var clan_btn = create_ninja_button("🏮 CHOISIR CLAN", Color.ORANGE)
	clan_btn.pressed.connect(_on_clan_selection_pressed)
	buttons_container.add_child(clan_btn)
	
	# Bouton Paramètres
	var settings_btn = create_ninja_button("⚙️ PARAMÈTRES", Color.GRAY)
	settings_btn.pressed.connect(_on_settings_pressed)
	buttons_container.add_child(settings_btn)
	
	# Bouton Quitter
	var quit_btn = create_ninja_button("🚪 QUITTER", Color.RED)
	quit_btn.pressed.connect(_on_quit_pressed)
	buttons_container.add_child(quit_btn)

func create_ninja_button(text: String, color: Color) -> Button:
	var button = Button.new()
	button.text = text
	button.custom_minimum_size = Vector2(300, 60)
	
	# Style ninja pour le bouton
	button.add_theme_font_size_override("font_size", 18)
	button.add_theme_color_override("font_color", Color.WHITE)
	button.add_theme_color_override("font_hover_color", color)
	button.add_theme_color_override("font_pressed_color", color)
	
	# Effet hover
	button.mouse_entered.connect(_on_button_hover.bind(button, color))
	button.mouse_exited.connect(_on_button_unhover.bind(button))
	
	return button

func create_clan_selection():
	# Panel de sélection de clan
	clan_selection_panel = Panel.new()
	clan_selection_panel.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	clan_selection_panel.size = Vector2(500, 400)
	clan_selection_panel.modulate = Color(0.1, 0.1, 0.1, 0.95)
	clan_selection_panel.visible = false
	add_child(clan_selection_panel)
	
	var vbox = VBoxContainer.new()
	vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	vbox.add_theme_constant_override("separation", 15)
	clan_selection_panel.add_child(vbox)
	
	# Titre
	var clan_title = Label.new()
	clan_title.text = "🏮 CHOISISSEZ VOTRE CLAN 🏮"
	clan_title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	clan_title.add_theme_font_size_override("font_size", 24)
	clan_title.add_theme_color_override("font_color", Color.GOLD)
	vbox.add_child(clan_title)
	
	# Clans
	var clans = [
		{"id": "ryujin", "name": "🔥 Clan Ryūjin", "desc": "Maîtres du Feu\n+2 Attaque, Résistance Feu", "color": Color.RED},
		{"id": "yurei", "name": "👤 Clan Yūrei", "desc": "Assassins de l'Ombre\n+3 Furtivité, Bonus Genjutsu", "color": Color.PURPLE},
		{"id": "kaze", "name": "💨 Clan Kaze", "desc": "Guerriers du Vent\n+2 Vitesse, Bonus Vent", "color": Color.CYAN}
	]
	
	for clan in clans:
		var clan_btn = create_clan_button(clan)
		vbox.add_child(clan_btn)
	
	# Boutons
	var btn_hbox = HBoxContainer.new()
	btn_hbox.alignment = BoxContainer.ALIGNMENT_CENTER
	vbox.add_child(btn_hbox)
	
	var confirm_btn = create_ninja_button("✅ CONFIRMER", Color.GREEN)
	confirm_btn.custom_minimum_size = Vector2(150, 50)
	confirm_btn.pressed.connect(_on_clan_confirmed)
	btn_hbox.add_child(confirm_btn)
	
	var cancel_btn = create_ninja_button("❌ ANNULER", Color.RED)
	cancel_btn.custom_minimum_size = Vector2(150, 50)
	cancel_btn.pressed.connect(_on_clan_cancelled)
	btn_hbox.add_child(cancel_btn)

func create_clan_button(clan_data: Dictionary) -> Button:
	var button = Button.new()
	button.text = clan_data.name + "\n" + clan_data.desc
	button.custom_minimum_size = Vector2(450, 80)
	button.add_theme_font_size_override("font_size", 16)
	button.add_theme_color_override("font_color", clan_data.color)
	
	# Sélection
	button.pressed.connect(_on_clan_selected.bind(clan_data.id))
	
	return button

func create_settings_panel():
	# Panel des paramètres
	settings_panel = Panel.new()
	settings_panel.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	settings_panel.size = Vector2(400, 300)
	settings_panel.modulate = Color(0.1, 0.1, 0.1, 0.95)
	settings_panel.visible = false
	add_child(settings_panel)
	
	var vbox = VBoxContainer.new()
	vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	settings_panel.add_child(vbox)
	
	# Titre
	var settings_title = Label.new()
	settings_title.text = "⚙️ PARAMÈTRES"
	settings_title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	settings_title.add_theme_font_size_override("font_size", 24)
	settings_title.add_theme_color_override("font_color", Color.GOLD)
	vbox.add_child(settings_title)
	
	# Options (placeholder)
	var options_label = Label.new()
	options_label.text = "🔊 Volume: 100%\n🎮 Contrôles: Tactile\n📱 Mode: Mobile\n🌐 Langue: Français"
	options_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	options_label.add_theme_font_size_override("font_size", 16)
	options_label.add_theme_color_override("font_color", Color.WHITE)
	vbox.add_child(options_label)
	
	# Bouton fermer
	var close_btn = create_ninja_button("❌ FERMER", Color.RED)
	close_btn.custom_minimum_size = Vector2(200, 50)
	close_btn.pressed.connect(_on_settings_closed)
	vbox.add_child(close_btn)

# Callbacks des boutons
func _on_new_game_pressed():
	print("🆕 Nouvelle partie")
	emit_signal("start_game", selected_clan, player_name)

func _on_continue_pressed():
	print("▶️ Continuer partie")
	# Charger la sauvegarde
	emit_signal("start_game", selected_clan, player_name)

func _on_clan_selection_pressed():
	clan_selection_panel.visible = true
	buttons_container.modulate.a = 0.3

func _on_settings_pressed():
	settings_panel.visible = true
	buttons_container.modulate.a = 0.3

func _on_quit_pressed():
	emit_signal("quit_game")

func _on_clan_selected(clan_id: String):
	selected_clan = clan_id
	print("🏮 Clan sélectionné: ", clan_id)

func _on_clan_confirmed():
	clan_selection_panel.visible = false
	buttons_container.modulate.a = 1.0
	print("✅ Clan confirmé: ", selected_clan)

func _on_clan_cancelled():
	clan_selection_panel.visible = false
	buttons_container.modulate.a = 1.0

func _on_settings_closed():
	settings_panel.visible = false
	buttons_container.modulate.a = 1.0

func _on_button_hover(button: Button, color: Color):
	var tween = create_tween()
	tween.tween_property(button, "modulate", color, 0.2)

func _on_button_unhover(button: Button):
	var tween = create_tween()
	tween.tween_property(button, "modulate", Color.WHITE, 0.2)

func has_save_file() -> bool:
	# Vérifier si un fichier de sauvegarde existe
	return FileAccess.file_exists("user://save_game.dat")
