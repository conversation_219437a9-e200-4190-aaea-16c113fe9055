extends Node

# Gestionnaire d'état global pour Shinobi Chronicles
class_name GameState

signal player_stats_changed
signal chakra_changed
signal level_up(new_level)
signal item_added(item_id, quantity)
signal region_unlocked(region_id)

# Données du joueur
var player_data = {
	"name": "Ninja",
	"clan": "ryujin",
	"level": 1,
	"xp": 0,
	"hp": 100,
	"max_hp": 100,
	"chakra": 20,
	"max_chakra": 20,
	"attack": 10,
	"defense": 5,
	"speed": 8,
	"stealth": 3,
	"position": Vector2(50, 50),
	"current_region": "starting_village"
}

# Inventaire
var inventory = {}

# Jutsu appris
var learned_jutsu = []

# Régions débloquées
var unlocked_regions = ["starting_village"]

# Zones explorées (pour fog of war)
var explored_tiles = {}

# Statistiques de jeu
var game_stats = {
	"battles_won": 0,
	"enemies_defeated": 0,
	"total_damage_dealt": 0,
	"jutsu_used": 0,
	"distance_traveled": 0,
	"play_time": 0.0
}

# Timer pour la régénération de chakra
var chakra_regen_timer = 0.0
const CHAKRA_REGEN_INTERVAL = 60.0  # 1 minute

func _ready():
	# Initialiser les données selon le clan choisi
	apply_clan_bonuses()
	
	# Démarrer le timer de régénération
	set_process(true)

func _process(delta):
	# Régénération de chakra
	chakra_regen_timer += delta
	if chakra_regen_timer >= CHAKRA_REGEN_INTERVAL:
		regenerate_chakra()
		chakra_regen_timer = 0.0
	
	# Mettre à jour le temps de jeu
	game_stats.play_time += delta

# Appliquer les bonus du clan
func apply_clan_bonuses():
	var clan_data = GameConfig.get_clan_data(player_data.clan)
	if clan_data.is_empty():
		return
	
	# Appliquer les bonus selon le clan
	if clan_data.has("bonus_attack"):
		player_data.attack += clan_data.bonus_attack
	
	if clan_data.has("bonus_stealth"):
		player_data.stealth += clan_data.bonus_stealth
	
	if clan_data.has("bonus_speed"):
		player_data.speed += clan_data.bonus_speed

# Régénération de chakra
func regenerate_chakra():
	if player_data.chakra < player_data.max_chakra:
		var regen_amount = GameConfig.CHAKRA_CONFIG.regen_rate
		
		# Bonus de clan pour la régénération
		var clan_data = GameConfig.get_clan_data(player_data.clan)
		if clan_data.has("bonus_chakra_regen"):
			regen_amount *= (1.0 + clan_data.bonus_chakra_regen)
		
		player_data.chakra = min(player_data.chakra + regen_amount, player_data.max_chakra)
		emit_signal("chakra_changed")

# Utiliser du chakra
func use_chakra(amount: int) -> bool:
	if GameConfig.debug_config.infinite_chakra:
		return true
	
	if player_data.chakra >= amount:
		player_data.chakra -= amount
		emit_signal("chakra_changed")
		return true
	return false

# Gagner de l'XP
func gain_xp(amount: int):
	player_data.xp += amount
	check_level_up()
	emit_signal("player_stats_changed")

# Vérifier si le joueur monte de niveau
func check_level_up():
	var xp_needed = GameConfig.get_xp_for_level(player_data.level + 1)
	
	while player_data.xp >= xp_needed and player_data.level < GameConfig.PROGRESSION_CONFIG.max_level:
		level_up()
		xp_needed = GameConfig.get_xp_for_level(player_data.level + 1)

# Monter de niveau
func level_up():
	player_data.level += 1
	
	# Augmenter les stats
	var hp_increase = 15 + randi() % 10
	var chakra_increase = 3 + randi() % 5
	var attack_increase = 2 + randi() % 3
	var defense_increase = 1 + randi() % 3
	var speed_increase = 1 + randi() % 2
	
	player_data.max_hp += hp_increase
	player_data.hp = player_data.max_hp  # Heal complet au level up
	player_data.max_chakra += chakra_increase
	player_data.chakra = player_data.max_chakra
	player_data.attack += attack_increase
	player_data.defense += defense_increase
	player_data.speed += speed_increase
	
	emit_signal("level_up", player_data.level)
	emit_signal("player_stats_changed")
	emit_signal("chakra_changed")

# Ajouter un objet à l'inventaire
func add_item(item_id: String, quantity: int = 1):
	if inventory.has(item_id):
		inventory[item_id] += quantity
	else:
		inventory[item_id] = quantity
	
	emit_signal("item_added", item_id, quantity)

# Utiliser un objet
func use_item(item_id: String) -> bool:
	if not inventory.has(item_id) or inventory[item_id] <= 0:
		return false
	
	inventory[item_id] -= 1
	if inventory[item_id] <= 0:
		inventory.erase(item_id)
	
	# Appliquer l'effet de l'objet (à implémenter selon les objets)
	apply_item_effect(item_id)
	return true

# Appliquer l'effet d'un objet
func apply_item_effect(item_id: String):
	match item_id:
		"chakra_pill":
			player_data.chakra = min(player_data.chakra + 10, player_data.max_chakra)
			emit_signal("chakra_changed")
		"health_potion":
			player_data.hp = min(player_data.hp + 30, player_data.max_hp)
			emit_signal("player_stats_changed")

# Déplacer le joueur
func move_player(new_position: Vector2):
	var old_position = player_data.position
	player_data.position = new_position
	
	# Calculer la distance parcourue
	var distance = old_position.distance_to(new_position)
	game_stats.distance_traveled += distance
	
	# Coût en chakra pour le mouvement
	if not use_chakra(GameConfig.CHAKRA_CONFIG.movement_cost):
		# Si pas assez de chakra, ne pas bouger
		player_data.position = old_position
		return false
	
	return true

# Débloquer une région
func unlock_region(region_id: String):
	if region_id not in unlocked_regions:
		unlocked_regions.append(region_id)
		emit_signal("region_unlocked", region_id)

# Marquer une tuile comme explorée
func explore_tile(tile_pos: Vector2):
	var key = str(tile_pos.x) + "," + str(tile_pos.y)
	explored_tiles[key] = true

# Vérifier si une tuile est explorée
func is_tile_explored(tile_pos: Vector2) -> bool:
	var key = str(tile_pos.x) + "," + str(tile_pos.y)
	return explored_tiles.has(key)

# Obtenir les données de sauvegarde
func get_save_data() -> Dictionary:
	return {
		"player_data": player_data,
		"inventory": inventory,
		"learned_jutsu": learned_jutsu,
		"unlocked_regions": unlocked_regions,
		"explored_tiles": explored_tiles,
		"game_stats": game_stats,
		"version": GameConfig.GAME_VERSION
	}

# Charger les données de sauvegarde
func load_save_data(data: Dictionary):
	if data.has("player_data"):
		player_data = data.player_data
	if data.has("inventory"):
		inventory = data.inventory
	if data.has("learned_jutsu"):
		learned_jutsu = data.learned_jutsu
	if data.has("unlocked_regions"):
		unlocked_regions = data.unlocked_regions
	if data.has("explored_tiles"):
		explored_tiles = data.explored_tiles
	if data.has("game_stats"):
		game_stats = data.game_stats
	
	# Réémettre les signaux pour mettre à jour l'UI
	emit_signal("player_stats_changed")
	emit_signal("chakra_changed")
