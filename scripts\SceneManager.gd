extends Node

# Gestionnaire de scènes pour Shinobi Chronicles
class_name SceneManager

var current_scene: Node
var game_state: GameState
var combat_system: CombatSystem
var save_system: SaveSystem
var data_loader: DataLoader

# Références des scènes
var exploration_scene_path = "res://scenes/ExplorationScene.tscn"
var battle_scene_path = "res://scenes/BattleScene.tscn"

signal scene_changed(scene_name)

func _ready():
	# Initialiser les systèmes globaux
	setup_global_systems()
	
	# Charger la scène d'exploration par défaut
	change_to_exploration()

func setup_global_systems():
	# Créer les instances des systèmes globaux
	data_loader = DataLoader.new()
	game_state = GameState.new()
	combat_system = CombatSystem.new()
	save_system = SaveSystem.new()

	# Les ajouter comme enfants pour qu'ils persistent
	add_child(data_loader)
	add_child(game_state)
	add_child(combat_system)
	add_child(save_system)

	# Initialiser le système de combat avec le data loader
	combat_system.initialize_with_data_loader(data_loader)

	# Connecter les signaux du système de combat
	combat_system.combat_ended.connect(_on_combat_ended)

func change_scene(scene_path: String):
	# Libérer la scène actuelle
	if current_scene:
		current_scene.queue_free()
		await current_scene.tree_exited
	
	# Charger la nouvelle scène
	var new_scene = load(scene_path).instantiate()
	get_tree().root.add_child(new_scene)
	current_scene = new_scene
	
	return new_scene

func change_to_exploration():
	var exploration_scene = change_scene(exploration_scene_path)
	await exploration_scene.ready
	
	# Initialiser la scène d'exploration avec les systèmes globaux
	if exploration_scene.has_method("initialize_with_systems"):
		exploration_scene.initialize_with_systems(game_state, combat_system, save_system)
	
	# Connecter le signal d'encounter
	if exploration_scene.has_signal("encounter_triggered"):
		exploration_scene.encounter_triggered.connect(_on_encounter_triggered)
	
	emit_signal("scene_changed", "exploration")

func change_to_battle(enemy_id: String):
	var battle_scene = change_scene(battle_scene_path)
	await battle_scene.ready
	
	# Démarrer le combat
	combat_system.start_combat(enemy_id, game_state)
	
	# Initialiser la scène de combat
	var enemy_data = combat_system.current_enemy
	if battle_scene.has_method("initialize_battle"):
		battle_scene.initialize_battle(enemy_data, game_state, combat_system)
	
	# Connecter le signal de fin de combat
	if battle_scene.has_signal("battle_finished"):
		battle_scene.battle_finished.connect(_on_battle_finished)
	
	emit_signal("scene_changed", "battle")

func _on_encounter_triggered(enemy_id: String):
	change_to_battle(enemy_id)

func _on_battle_finished(victory: bool, rewards: Dictionary):
	# Retourner à l'exploration après le combat
	change_to_exploration()

func _on_combat_ended(victory: bool, rewards: Dictionary):
	# Ce signal est émis par le système de combat
	# Il peut être utilisé pour des effets globaux ou des statistiques
	pass

# Fonctions de sauvegarde/chargement globales
func save_game(slot: int) -> bool:
	return save_system.save_game(slot, game_state)

func load_game(slot: int) -> bool:
	var success = save_system.load_game(slot, game_state)
	if success:
		# Rafraîchir la scène actuelle si nécessaire
		if current_scene and current_scene.has_method("refresh_from_game_state"):
			current_scene.refresh_from_game_state()
	return success

func get_save_info(slot: int) -> Dictionary:
	return save_system.get_save_info(slot)

func delete_save(slot: int) -> bool:
	return save_system.delete_save(slot)

# Fonction pour quitter le jeu proprement
func quit_game():
	# Sauvegarder automatiquement si nécessaire
	if game_state and game_state.player_data.level > 1:
		save_game(1)  # Sauvegarde automatique dans le slot 1
	
	get_tree().quit()
