extends Node

# Scène principale pour Shinobi Chronicles
class_name Main

var scene_manager: SceneManager

func _ready():
	# Créer et initialiser le gestionnaire de scènes
	scene_manager = SceneManager.new()
	scene_manager.name = "SceneManager"
	add_child(scene_manager)
	
	# Connecter les signaux
	scene_manager.scene_changed.connect(_on_scene_changed)
	
	print("Shinobi Chronicles - Jeu démarré!")

func _on_scene_changed(scene_name: String):
	print("Scène changée vers: ", scene_name)

func _input(event):
	# Gestion des raccourcis globaux
	if event.is_action_pressed("ui_cancel"):  # Échap
		show_pause_menu()

func show_pause_menu():
	# Créer un menu de pause simple
	var pause_menu = AcceptDialog.new()
	pause_menu.title = "Menu de Pause"
	pause_menu.size = Vector2(300, 200)
	
	var vbox = VBoxContainer.new()
	pause_menu.add_child(vbox)
	
	# Bouton Continuer
	var continue_button = Button.new()
	continue_button.text = "Continuer"
	continue_button.pressed.connect(pause_menu.queue_free)
	vbox.add_child(continue_button)
	
	# Bouton Sauvegarder
	var save_button = Button.new()
	save_button.text = "Sauvegarder"
	save_button.pressed.connect(_on_save_pressed.bind(pause_menu))
	vbox.add_child(save_button)
	
	# Bouton Charger
	var load_button = Button.new()
	load_button.text = "Charger"
	load_button.pressed.connect(_on_load_pressed.bind(pause_menu))
	vbox.add_child(load_button)
	
	# Bouton Quitter
	var quit_button = Button.new()
	quit_button.text = "Quitter"
	quit_button.pressed.connect(_on_quit_pressed)
	vbox.add_child(quit_button)
	
	add_child(pause_menu)
	pause_menu.popup_centered()

func _on_save_pressed(menu: AcceptDialog):
	# Sauvegarder dans le slot 1
	if scene_manager.save_game(1):
		print("Jeu sauvegardé!")
	else:
		print("Erreur de sauvegarde!")
	menu.queue_free()

func _on_load_pressed(menu: AcceptDialog):
	# Charger depuis le slot 1
	if scene_manager.load_game(1):
		print("Jeu chargé!")
	else:
		print("Erreur de chargement!")
	menu.queue_free()

func _on_quit_pressed():
	scene_manager.quit_game()
