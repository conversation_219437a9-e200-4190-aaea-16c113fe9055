extends Node

# Version avancée de Shinobi Chronicles avec systèmes complets
class_name AdvancedGame

var game_state: GameStateFixed
var player_sprite: Sprite2D
var camera: Camera2D
var hud_container: Control
var minimap: Control

# UI Elements
var hp_bar: ProgressBar
var chakra_bar: ProgressBar
var xp_bar: ProgressBar
var level_label: Label
var position_label: Label
var clan_label: Label
var message_label: Label

var is_moving: bool = false
var message_timer: Timer

func _ready():
	print("🥷 Shinobi Chronicles - Version Avancée")
	setup_game()

func setup_game():
	# C<PERSON>er le GameState
	game_state = GameStateFixed.new()
	add_child(game_state)
	
	# Connecter les signaux
	game_state.player_stats_changed.connect(_on_player_stats_changed)
	game_state.chakra_changed.connect(_on_chakra_changed)
	game_state.level_up.connect(_on_level_up)
	game_state.item_added.connect(_on_item_added)
	
	# C<PERSON>er le joueur
	setup_player()
	
	# Créer l'interface
	setup_hud()
	
	# Configurer les inputs
	set_process_input(true)
	
	print("Jeu avancé initialisé!")
	print(game_state.get_player_summary())

func setup_player():
	# Créer le sprite du joueur
	player_sprite = Sprite2D.new()
	player_sprite.name = "Player"
	
	# Créer une texture ninja (carré rouge avec bordure)
	var image = Image.create(32, 32, false, Image.FORMAT_RGB8)
	
	# Remplir avec du rouge
	image.fill(Color.RED)
	
	# Ajouter une bordure noire
	for x in range(32):
		for y in range(32):
			if x == 0 or x == 31 or y == 0 or y == 31:
				image.set_pixel(x, y, Color.BLACK)
			elif x < 4 or x > 27 or y < 4 or y > 27:
				image.set_pixel(x, y, Color.DARK_RED)
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	player_sprite.texture = texture
	
	add_child(player_sprite)
	
	# Position initiale
	update_player_position()
	
	# Créer la caméra
	camera = Camera2D.new()
	camera.name = "Camera"
	camera.enabled = true
	player_sprite.add_child(camera)

func update_player_position():
	var pixel_pos = game_state.player_data.position * 32
	player_sprite.position = pixel_pos

func setup_hud():
	# Container principal pour l'interface
	hud_container = Control.new()
	hud_container.name = "HUD"
	hud_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	hud_container.mouse_filter = Control.MOUSE_FILTER_IGNORE
	add_child(hud_container)
	
	# Panel d'informations principal
	var info_panel = Panel.new()
	info_panel.position = Vector2(10, 10)
	info_panel.size = Vector2(300, 200)
	hud_container.add_child(info_panel)
	
	var vbox = VBoxContainer.new()
	vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	vbox.add_theme_constant_override("separation", 5)
	info_panel.add_child(vbox)
	
	# Titre et niveau
	var title_hbox = HBoxContainer.new()
	vbox.add_child(title_hbox)
	
	var title_label = Label.new()
	title_label.text = "🥷 Shinobi Chronicles"
	title_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	title_hbox.add_child(title_label)
	
	level_label = Label.new()
	level_label.text = "Niv. " + str(game_state.player_data.level)
	level_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
	title_hbox.add_child(level_label)
	
	# HP Bar
	var hp_hbox = HBoxContainer.new()
	vbox.add_child(hp_hbox)
	
	var hp_icon = Label.new()
	hp_icon.text = "❤️"
	hp_hbox.add_child(hp_icon)
	
	hp_bar = ProgressBar.new()
	hp_bar.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	hp_bar.show_percentage = false
	hp_bar.max_value = game_state.player_data.max_hp
	hp_bar.value = game_state.player_data.hp
	hp_hbox.add_child(hp_bar)
	
	var hp_label = Label.new()
	hp_label.text = str(game_state.player_data.hp) + "/" + str(game_state.player_data.max_hp)
	hp_hbox.add_child(hp_label)
	
	# Chakra Bar
	var chakra_hbox = HBoxContainer.new()
	vbox.add_child(chakra_hbox)
	
	var chakra_icon = Label.new()
	chakra_icon.text = "🔵"
	chakra_hbox.add_child(chakra_icon)
	
	chakra_bar = ProgressBar.new()
	chakra_bar.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	chakra_bar.show_percentage = false
	chakra_bar.modulate = Color.CYAN
	chakra_bar.max_value = game_state.player_data.max_chakra
	chakra_bar.value = game_state.player_data.chakra
	chakra_hbox.add_child(chakra_bar)
	
	var chakra_label = Label.new()
	chakra_label.text = str(game_state.player_data.chakra) + "/" + str(game_state.player_data.max_chakra)
	chakra_hbox.add_child(chakra_label)
	
	# XP Bar
	var xp_hbox = HBoxContainer.new()
	vbox.add_child(xp_hbox)
	
	var xp_icon = Label.new()
	xp_icon.text = "⭐"
	xp_hbox.add_child(xp_icon)
	
	xp_bar = ProgressBar.new()
	xp_bar.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	xp_bar.show_percentage = false
	xp_bar.modulate = Color.YELLOW
	update_xp_bar()
	xp_hbox.add_child(xp_bar)
	
	var xp_label = Label.new()
	var xp_needed = game_state.get_xp_for_level(game_state.player_data.level + 1)
	xp_label.text = str(game_state.player_data.xp) + "/" + str(xp_needed)
	xp_hbox.add_child(xp_label)
	
	# Clan
	clan_label = Label.new()
	clan_label.text = "Clan: " + game_state.player_data.clan.capitalize()
	clan_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(clan_label)
	
	# Position
	position_label = Label.new()
	position_label.text = "Position: " + str(game_state.player_data.position)
	position_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(position_label)
	
	# Minimap simple
	setup_minimap()
	
	# Zone de messages
	message_label = Label.new()
	message_label.position = Vector2(10, 250)
	message_label.size = Vector2(400, 50)
	message_label.add_theme_color_override("font_color", Color.YELLOW)
	message_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	hud_container.add_child(message_label)
	
	# Timer pour les messages
	message_timer = Timer.new()
	message_timer.wait_time = 3.0
	message_timer.one_shot = true
	message_timer.timeout.connect(_on_message_timeout)
	add_child(message_timer)

func setup_minimap():
	# Minimap simple
	minimap = Panel.new()
	minimap.position = Vector2(get_viewport().size.x - 160, 10)
	minimap.size = Vector2(150, 150)
	minimap.anchor_left = 1.0
	minimap.anchor_right = 1.0
	minimap.offset_left = -160
	minimap.offset_right = -10
	hud_container.add_child(minimap)
	
	var minimap_label = Label.new()
	minimap_label.text = "Minimap"
	minimap_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	minimap_label.position = Vector2(0, 5)
	minimap_label.size = Vector2(150, 20)
	minimap.add_child(minimap_label)
	
	# Point du joueur
	var player_dot = ColorRect.new()
	player_dot.color = Color.RED
	player_dot.size = Vector2(6, 6)
	player_dot.position = Vector2(72, 72)  # Centre
	minimap.add_child(player_dot)

func update_xp_bar():
	var current_level_xp = game_state.get_xp_for_level(game_state.player_data.level)
	var next_level_xp = game_state.get_xp_for_level(game_state.player_data.level + 1)
	var progress = game_state.player_data.xp - current_level_xp
	var needed = next_level_xp - current_level_xp
	
	xp_bar.max_value = needed
	xp_bar.value = progress

func _input(event):
	if is_moving:
		return
	
	var movement_vector = Vector2.ZERO
	
	# Gestion des mouvements
	if Input.is_action_pressed("move_up"):
		movement_vector.y = -1
	elif Input.is_action_pressed("move_down"):
		movement_vector.y = 1
	elif Input.is_action_pressed("move_left"):
		movement_vector.x = -1
	elif Input.is_action_pressed("move_right"):
		movement_vector.x = 1
	
	if movement_vector != Vector2.ZERO:
		move_player(movement_vector)
	
	# Actions spéciales
	if Input.is_action_just_pressed("scan"):
		scan_area()
	elif Input.is_action_just_pressed("interact"):
		interact()
	elif Input.is_action_just_pressed("ui_cancel"):
		show_pause_menu()

func move_player(direction: Vector2):
	var new_world_pos = game_state.player_data.position + direction
	
	# Vérifier les limites du monde
	if new_world_pos.x < 0 or new_world_pos.x >= 100:
		return
	if new_world_pos.y < 0 or new_world_pos.y >= 100:
		return
	
	# Essayer de déplacer le joueur (utilise automatiquement le chakra)
	if not game_state.move_player(new_world_pos):
		show_message("Pas assez de chakra pour bouger!")
		return
	
	# Animation de mouvement
	is_moving = true
	var new_pixel_pos = new_world_pos * 32
	
	var tween = create_tween()
	tween.tween_property(player_sprite, "position", new_pixel_pos, 0.2)
	await tween.finished
	
	# Marquer la tuile comme explorée
	game_state.explore_tile(new_world_pos)
	
	# Chance de rencontre
	if randf() < 0.08:  # 8% de chance
		trigger_encounter()
	
	# Mettre à jour l'interface
	update_ui()
	
	is_moving = false

func scan_area():
	if not game_state.use_chakra(5):
		show_message("Pas assez de chakra pour scanner!")
		return
	
	# Révéler une zone autour du joueur
	var scan_radius = 3
	var player_tile = game_state.player_data.position
	
	for x in range(-scan_radius, scan_radius + 1):
		for y in range(-scan_radius, scan_radius + 1):
			var tile_pos = player_tile + Vector2(x, y)
			if tile_pos.x >= 0 and tile_pos.x < 100 and tile_pos.y >= 0 and tile_pos.y < 100:
				game_state.explore_tile(tile_pos)
	
	show_message("Zone scannée! Rayon: " + str(scan_radius) + " tuiles")

func trigger_encounter():
	var enemies = ["Sanglier Sauvage", "Loup de Forêt", "Ninja de l'Ombre"]
	var enemy = enemies[randi() % enemies.size()]
	
	show_message("🥷 Rencontre avec: " + enemy + "!")
	
	# Combat simplifié avec plus de récompenses
	var xp_gain = 30 + randi() % 20
	var item_chance = randf()
	
	game_state.gain_xp(xp_gain)
	game_state.game_stats.battles_won += 1
	game_state.game_stats.enemies_defeated += 1
	
	if item_chance < 0.3:
		game_state.add_item("chakra_pill", 1)
	elif item_chance < 0.5:
		game_state.add_item("health_potion", 1)

func interact():
	show_message("Vous fouillez les alentours...")
	
	# Chance de trouver quelque chose
	if randf() < 0.2:
		var items = ["chakra_pill", "health_potion"]
		var item = items[randi() % items.size()]
		game_state.add_item(item, 1)
		show_message("Vous trouvez: " + item + "!")
	else:
		show_message("Rien d'intéressant ici.")

func show_message(text: String):
	print("Message: ", text)
	message_label.text = text
	message_label.visible = true
	message_timer.start()

func _on_message_timeout():
	message_label.visible = false

func update_ui():
	position_label.text = "Position: " + str(game_state.player_data.position)

func show_pause_menu():
	var pause_menu = AcceptDialog.new()
	pause_menu.title = "🥷 Menu Ninja"
	pause_menu.size = Vector2(400, 400)
	
	var vbox = VBoxContainer.new()
	pause_menu.add_child(vbox)
	
	# Statistiques détaillées
	var stats_label = Label.new()
	stats_label.text = game_state.get_player_summary() + "\n\n"
	stats_label.text += "📊 Statistiques:\n"
	stats_label.text += "Combats gagnés: " + str(game_state.game_stats.battles_won) + "\n"
	stats_label.text += "Ennemis vaincus: " + str(game_state.game_stats.enemies_defeated) + "\n"
	stats_label.text += "Distance parcourue: " + str(int(game_state.game_stats.distance_traveled)) + "\n"
	stats_label.text += "Temps de jeu: " + str(int(game_state.game_stats.play_time)) + "s\n\n"
	
	if not game_state.inventory.is_empty():
		stats_label.text += "🎒 Inventaire:\n"
		for item_id in game_state.inventory.keys():
			stats_label.text += "  " + item_id + " x" + str(game_state.inventory[item_id]) + "\n"
	
	vbox.add_child(stats_label)
	
	var separator = HSeparator.new()
	vbox.add_child(separator)
	
	# Boutons d'action
	var continue_button = Button.new()
	continue_button.text = "Continuer l'Aventure"
	continue_button.pressed.connect(pause_menu.queue_free)
	vbox.add_child(continue_button)
	
	var combat_button = Button.new()
	combat_button.text = "Combat d'Entraînement"
	combat_button.pressed.connect(_on_training_combat.bind(pause_menu))
	vbox.add_child(combat_button)
	
	var heal_button = Button.new()
	heal_button.text = "Méditation (Restaurer)"
	heal_button.pressed.connect(_on_meditation.bind(pause_menu))
	vbox.add_child(heal_button)
	
	var quit_button = Button.new()
	quit_button.text = "Quitter"
	quit_button.pressed.connect(get_tree().quit)
	vbox.add_child(quit_button)
	
	add_child(pause_menu)
	pause_menu.popup_centered()

func _on_training_combat(menu: AcceptDialog):
	show_message("🥷 Combat d'entraînement terminé!")
	game_state.gain_xp(75)
	menu.queue_free()

func _on_meditation(menu: AcceptDialog):
	game_state.player_data.hp = game_state.player_data.max_hp
	game_state.player_data.chakra = game_state.player_data.max_chakra
	show_message("🧘 Méditation terminée. HP et Chakra restaurés!")
	update_all_ui()
	menu.queue_free()

# Callbacks des signaux
func _on_player_stats_changed():
	update_all_ui()

func _on_chakra_changed():
	update_all_ui()

func _on_level_up(new_level: int):
	show_message("🎉 NIVEAU SUPÉRIEUR! Nouveau niveau: " + str(new_level))
	update_all_ui()

func _on_item_added(item_id: String, quantity: int):
	show_message("📦 Objet obtenu: " + item_id + " x" + str(quantity))

func update_all_ui():
	if hp_bar:
		hp_bar.max_value = game_state.player_data.max_hp
		hp_bar.value = game_state.player_data.hp
	
	if chakra_bar:
		chakra_bar.max_value = game_state.player_data.max_chakra
		chakra_bar.value = game_state.player_data.chakra
	
	if level_label:
		level_label.text = "Niv. " + str(game_state.player_data.level)
	
	if xp_bar:
		update_xp_bar()
	
	update_ui()
