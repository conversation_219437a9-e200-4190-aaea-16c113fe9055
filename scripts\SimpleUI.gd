extends Node2D

# Version ultra-simple avec interface directe

# Données du joueur
var player_data = {
	"name": "Ninja",
	"level": 1,
	"hp": 100,
	"max_hp": 100,
	"chakra": 20,
	"max_chakra": 20,
	"attack": 12,
	"position": Vector2(50, 50)
}

var inventory = {}
var is_moving = false
var chakra_timer = 0.0

# Éléments visuels
var player_sprite: Sprite2D
var camera: Camera2D
var ui_labels: Array = []

func _ready():
	print("🥷 Shinobi Chronicles - Interface Simple")
	setup_simple_game()

func setup_simple_game():
	# Créer le ninja
	create_ninja()
	
	# Créer l'interface DIRECTEMENT
	create_simple_ui()
	
	set_process_input(true)
	set_process(true)
	
	print("✅ Jeu simple créé!")

func create_ninja():
	# Sprite ninja
	player_sprite = Sprite2D.new()
	
	# Texture simple
	var image = Image.create(32, 32, false, Image.FORMAT_RGB8)
	image.fill(Color.RED)
	# Bordure noire
	for i in range(32):
		image.set_pixel(i, 0, Color.BLACK)
		image.set_pixel(i, 31, Color.BLACK)
		image.set_pixel(0, i, Color.BLACK)
		image.set_pixel(31, i, Color.BLACK)
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	player_sprite.texture = texture
	player_sprite.position = Vector2(400, 300)  # Centre de l'écran
	
	add_child(player_sprite)
	
	# Caméra
	camera = Camera2D.new()
	camera.enabled = true
	player_sprite.add_child(camera)

func create_simple_ui():
	# Titre
	var title = Label.new()
	title.text = "🥷 SHINOBI CHRONICLES 🥷"
	title.position = Vector2(50, 50)
	title.add_theme_font_size_override("font_size", 24)
	title.add_theme_color_override("font_color", Color.GOLD)
	add_child(title)
	ui_labels.append(title)
	
	# Stats principales
	var stats = Label.new()
	stats.position = Vector2(50, 100)
	stats.add_theme_font_size_override("font_size", 16)
	stats.add_theme_color_override("font_color", Color.WHITE)
	add_child(stats)
	ui_labels.append(stats)
	
	# Position
	var pos_label = Label.new()
	pos_label.position = Vector2(50, 200)
	pos_label.add_theme_font_size_override("font_size", 14)
	pos_label.add_theme_color_override("font_color", Color.CYAN)
	add_child(pos_label)
	ui_labels.append(pos_label)
	
	# Inventaire
	var inv_label = Label.new()
	inv_label.position = Vector2(50, 250)
	inv_label.add_theme_font_size_override("font_size", 14)
	inv_label.add_theme_color_override("font_color", Color.YELLOW)
	add_child(inv_label)
	ui_labels.append(inv_label)
	
	# Contrôles
	var controls = Label.new()
	controls.text = "🎮 WASD: Bouger | F: Scanner | E: Fouiller | Échap: Menu"
	controls.position = Vector2(50, 350)
	controls.add_theme_font_size_override("font_size", 14)
	controls.add_theme_color_override("font_color", Color.LIGHT_GREEN)
	add_child(controls)
	ui_labels.append(controls)
	
	# Messages
	var message = Label.new()
	message.position = Vector2(50, 400)
	message.add_theme_font_size_override("font_size", 16)
	message.add_theme_color_override("font_color", Color.YELLOW)
	add_child(message)
	ui_labels.append(message)
	
	update_ui()

func _process(delta):
	# Régénération chakra
	chakra_timer += delta
	if chakra_timer >= 5.0:
		if player_data.chakra < player_data.max_chakra:
			player_data.chakra += 1
			update_ui()
		chakra_timer = 0.0

func _input(_event):
	if is_moving:
		return
	
	var movement = Vector2.ZERO
	
	if Input.is_action_pressed("move_up"):
		movement.y = -1
	elif Input.is_action_pressed("move_down"):
		movement.y = 1
	elif Input.is_action_pressed("move_left"):
		movement.x = -1
	elif Input.is_action_pressed("move_right"):
		movement.x = 1
	
	if movement != Vector2.ZERO:
		move_ninja(movement)
	
	if Input.is_action_just_pressed("scan"):
		scan()
	elif Input.is_action_just_pressed("interact"):
		search()
	elif Input.is_action_just_pressed("ui_cancel"):
		show_menu()

func move_ninja(direction: Vector2):
	if player_data.chakra < 1:
		show_message("💨 Pas assez de chakra!")
		return
	
	player_data.chakra -= 1
	player_data.position += direction
	
	# Limites
	player_data.position.x = clamp(player_data.position.x, 0, 99)
	player_data.position.y = clamp(player_data.position.y, 0, 99)
	
	# Animation
	is_moving = true
	var new_pos = player_sprite.position + direction * 32
	
	var tween = create_tween()
	tween.tween_property(player_sprite, "position", new_pos, 0.2)
	await tween.finished
	
	# Combat chance
	if randf() < 0.2:
		combat()
	
	update_ui()
	is_moving = false

func scan():
	if player_data.chakra < 5:
		show_message("💨 Pas assez de chakra!")
		return
	
	player_data.chakra -= 5
	show_message("👁️ Zone scannée!")
	update_ui()

func search():
	if randf() < 0.3:
		var items = ["Pilule Chakra", "Potion Santé", "Shuriken"]
		var item = items[randi() % items.size()]
		add_item(item)
		show_message("🎁 Trouvé: " + item + "!")
	else:
		show_message("🍃 Rien trouvé...")

func combat():
	var enemies = ["Sanglier", "Loup", "Ninja"]
	var enemy = enemies[randi() % enemies.size()]
	
	if randf() < 0.6:
		var xp = 20 + randi() % 20
		player_data.level += 1 if randf() < 0.1 else 0
		show_message("🏆 Victoire vs " + enemy + "! +" + str(xp) + " XP")
	else:
		var damage = 10 + randi() % 15
		player_data.hp = max(10, player_data.hp - damage)
		show_message("💥 Défaite vs " + enemy + "! -" + str(damage) + " HP")
	
	update_ui()

func add_item(item: String):
	if inventory.has(item):
		inventory[item] += 1
	else:
		inventory[item] = 1

func update_ui():
	if ui_labels.size() >= 4:
		# Stats
		ui_labels[1].text = "Niveau: " + str(player_data.level) + "\nHP: " + str(player_data.hp) + "/" + str(player_data.max_hp) + "\nChakra: " + str(player_data.chakra) + "/" + str(player_data.max_chakra) + "\nAttaque: " + str(player_data.attack)
		
		# Position
		ui_labels[2].text = "📍 Position: " + str(player_data.position)
		
		# Inventaire
		var inv_text = "🎒 Inventaire:\n"
		if inventory.is_empty():
			inv_text += "Vide"
		else:
			for item in inventory.keys():
				inv_text += item + " x" + str(inventory[item]) + "\n"
		ui_labels[3].text = inv_text

func show_message(text: String):
	print("🥷 ", text)
	if ui_labels.size() >= 6:
		ui_labels[5].text = text
		# Effacer après 3 secondes
		var timer = Timer.new()
		timer.wait_time = 3.0
		timer.one_shot = true
		timer.timeout.connect(func(): ui_labels[5].text = "")
		add_child(timer)
		timer.start()

func show_menu():
	var menu = AcceptDialog.new()
	menu.title = "🥷 Menu Ninja"
	menu.size = Vector2(300, 200)
	
	var vbox = VBoxContainer.new()
	menu.add_child(vbox)
	
	var info = Label.new()
	info.text = "Ninja Niveau " + str(player_data.level) + "\nHP: " + str(player_data.hp) + "/" + str(player_data.max_hp) + "\nChakra: " + str(player_data.chakra) + "/" + str(player_data.max_chakra)
	vbox.add_child(info)
	
	var continue_btn = Button.new()
	continue_btn.text = "Continuer"
	continue_btn.pressed.connect(menu.queue_free)
	vbox.add_child(continue_btn)
	
	var heal_btn = Button.new()
	heal_btn.text = "Méditation (Restaurer)"
	heal_btn.pressed.connect(_on_heal.bind(menu))
	vbox.add_child(heal_btn)
	
	var quit_btn = Button.new()
	quit_btn.text = "Quitter"
	quit_btn.pressed.connect(get_tree().quit)
	vbox.add_child(quit_btn)
	
	add_child(menu)
	menu.popup_centered()

func _on_heal(menu):
	player_data.hp = player_data.max_hp
	player_data.chakra = player_data.max_chakra
	show_message("🧘 Tout restauré!")
	update_ui()
	menu.queue_free()
