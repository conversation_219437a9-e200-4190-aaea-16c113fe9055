# 🥷 Guide de Développement - Shinobi Chronicles

## 🎯 Vue d'ensemble du Projet

Shinobi Chronicles est  une architecture complète et modulaire. Ce guide vous explique comment utiliser, modifier et étendre le jeu.

## 🚀 État Actuel du Projet

### ✅ **Systèmes Implémentés et Fonctionnels**

#### **🔧 Architecture de Base**
- ✅ Configuration centralisée (`GameConfig.js`)
- ✅ Gestion d'état globale (`GameState.js`)
- ✅ Système de modules interconnectés
- ✅ Structure de fichiers organisée

#### **💾 Système de Sauvegarde Avancé**
- ✅ Multi-slots (3 emplacements)
- ✅ Sauvegarde automatique
- ✅ Compression des données
- ✅ Vérification d'intégrité
- ✅ Import/Export de sauvegardes

#### **⚡ Système de Chakra Intelligent**
- ✅ Régénération automatique
- ✅ Coûts variables selon les actions
- ✅ Bonus de clan
- ✅ Interface visuelle dynamique
- ✅ Gestion offline/online

#### **⚔️ Système de Combat Complet**
- ✅ Combat tour par tour
- ✅ Éléments avec relations (feu > vent > terre, etc.)
- ✅ Jutsu avec effets de statut
- ✅ IA ennemie avec patterns
- ✅ Système de loot et récompenses

#### **🌫️ Fog of War Optimisé**
- ✅ Révélation progressive de la carte
- ✅ Cache pour les performances
- ✅ Sauvegarde des zones explorées
- ✅ Effets visuels

#### **🎮 Scènes Phaser**
- ✅ LoadingScene (chargement des assets)
- ✅ ExplorationScene (monde ouvert)
- ✅ BattleScene (combats)
- ✅ VillageScene (interactions sociales)

#### **🖥️ Interface Utilisateur**
- ✅ HUD temps réel
- ✅ Mini-carte interactive
- ✅ Contrôles mobiles adaptatifs
- ✅ Modales (inventaire, compétences, carte)
- ✅ Notifications système

#### **📊 Base de Données**
- ✅ 12 ennemis équilibrés (du sanglier au boss final)
- ✅ 20+ objets avec système de rareté
- ✅ 18 jutsu répartis en 3 branches
- ✅ Système de clans avec bonus uniques

## 🎮 Comment Jouer

### **Démarrage Rapide**
1. **Lancer le serveur** : `npm run dev`
2. **Ouvrir** : `http://localhost:8080`
3. **Tester** : `http://localhost:8080/test.html`

### **Contrôles**
- **Mouvement** : Flèches directionnelles ou WASD
- **Interaction** : Espace ou E
- **Scan** : F (coûte 5 chakra, révèle une zone)
- **Inventaire** : I
- **Compétences** : S
- **Carte** : M
- **Menu** : Échap

### **Système de Clans**

#### **🔥 Clan Ryūjin (Feu)**
- **Bonus** : +2 Attaque, +20% Résistance au feu
- **Jutsu** : Tranche du Dragon
- **Style** : Combattant offensif

#### **👤 Clan Yūrei (Ombre)**
- **Bonus** : +3 Furtivité, +30% Puissance Genjutsu
- **Jutsu** : Clone d'Ombre
- **Style** : Assassin furtif

#### **💨 Clan Kaze (Vent)**
- **Bonus** : +2 Vitesse, +25% Puissance du Vent
- **Jutsu** : Lame de Vent
- **Style** : Combattant rapide

## 🛠️ Développement et Extension

### **Ajouter un Nouvel Ennemi**

1. **Éditer** `data/enemies.json` :
```json
{
  "id": "mon_ennemi",
  "name": "Mon Ennemi",
  "level": 5,
  "hp": 80,
  "attack": 20,
  "element": "fire",
  "xp": 100,
  "ryo": 50,
  "loot": [
    {"id": "item_rare", "chance": 0.3, "quantity": 1}
  ]
}
```

2. **Ajouter le sprite** dans `assets/images/enemies/`
3. **Configurer les zones** d'apparition dans `spawn_regions`

### **Ajouter un Nouveau Jutsu**

1. **Éditer** `data/jutsu.json` :
```json
{
  "id": "mon_jutsu",
  "name": "Mon Jutsu",
  "type": "ninjutsu",
  "element": "fire",
  "chakra_cost": 25,
  "base_damage": 30,
  "requirements": {
    "level": 5,
    "clan": "ryujin"
  }
}
```

2. **Implémenter la logique** dans `CombatSystem.js`
3. **Ajouter les animations** et effets visuels

### **Ajouter une Nouvelle Région**

1. **Configurer** dans `GameConfig.js` :
```javascript
nouvelle_region: {
  name: "Nouvelle Région",
  levelRange: [8, 12],
  biome: "custom",
  encounterRate: 0.5,
  unlockLevel: 8
}
```

2. **Créer la carte** dans `data/maps/`
3. **Ajouter les tilesets** et sprites

### **Modifier l'Interface**

#### **Styles CSS**
- `styles/main.css` : Styles généraux
- `styles/ui.css` : Interface utilisateur
- `styles/combat.css` : Interface de combat

#### **Composants JavaScript**
- `js/ui/HUD.js` : Interface principale
- `js/ui/InventoryUI.js` : Inventaire (à créer)
- `js/ui/SkillTreeUI.js` : Arbre de compétences (à créer)

## 🔧 Configuration Avancée

### **GameConfig.js - Paramètres Principaux**

```javascript
// Modifier la difficulté
progression: {
  baseXP: 100,        // XP de base par niveau
  xpMultiplier: 1.5,  // Multiplicateur de progression
  maxLevel: 50        // Niveau maximum
},

// Ajuster le chakra
chakra: {
  baseAmount: 20,     // Chakra de départ
  regenRate: 1,       // Régénération par minute
  movementCost: 1     // Coût par mouvement
},

// Équilibrer le combat
combat: {
  damageMultipliers: {
    weakness: 1.5,    // Dégâts sur faiblesse
    resistance: 0.75  // Dégâts sur résistance
  }
}
```

### **Debug et Test**

#### **Mode Debug**
```javascript
// Dans GameConfig.js
debug: {
  enabled: true,
  godMode: true,           // Invincibilité
  unlockAllRegions: true,  // Toutes les régions
  startLevel: 10          // Niveau de départ
}
```

#### **Console Commands**
```javascript
// Gagner de l'XP
gameState.gainXP(1000);

// Ajouter des objets
gameState.addItem("chakra_pill", 10);

// Téléportation
gameState.updatePlayerPosition(50, 50);

// Révéler la carte
fogOfWar.revealAllMap();

// Changer de clan
gameState.player.clan = "yurei";
```

## 📈 Roadmap de Développement

### **Phase 1 : Finalisation MVP (Semaines 1-2)**
- [ ] Interface d'inventaire fonctionnelle
- [ ] Arbre de compétences interactif
- [ ] Système de quêtes basique
- [ ] Audio (musiques et effets)

### **Phase 2 : Contenu (Semaines 3-4)**
- [ ] 2 régions supplémentaires
- [ ] 10 ennemis additionnels
- [ ] Premier donjon complet
- [ ] Système de craft

### **Phase 3 : Polish (Semaines 5-6)**
- [ ] Animations avancées
- [ ] Effets visuels
- [ ] Optimisations performances
- [ ] Tests et équilibrage

## 🐛 Résolution de Problèmes

### **Erreurs Communes**

#### **"GameConfig is not defined"**
- Vérifier l'ordre de chargement des scripts dans `index.html`
- S'assurer que `GameConfig.js` est chargé en premier

#### **Sauvegarde ne fonctionne pas**
- Vérifier que localStorage est activé
- Tester avec `saveSystem.saveGame(1, true)`

#### **Phaser ne se lance pas**
- Vérifier la console pour les erreurs
- S'assurer que le CDN Phaser est accessible

#### **Assets ne se chargent pas**
- Vérifier les chemins dans `LoadingScene.js`
- Utiliser les placeholders en attendant les vrais assets

### **Performance**

#### **Optimisations**
```javascript
// Réduire la fréquence de mise à jour
GameConfig.debug.updateInterval = 200; // ms

// Limiter le fog of war
GameConfig.world.fogOfWarRadius = 2;

// Cache des tuiles
const tileCache = new Map();
```

## 📚 Ressources et Références

### **Documentation Phaser**
- [Phaser 3 API](https://photonstorm.github.io/phaser3-docs/)
- [Exemples Phaser](https://phaser.io/examples)

### **Assets Recommandés**
- **Sprites** : 32x32px, format PNG
- **Tilesets** : 32x32px, organisés en grilles
- **Audio** : OGG pour la musique, WAV pour les SFX

### **Outils de Développement**
- **Tiled** : Éditeur de cartes
- **Aseprite** : Création de sprites
- **Audacity** : Édition audio

## 🤝 Contribution

### **Structure des Commits**
```
feat: Ajouter nouveau système de quêtes
fix: Corriger bug de sauvegarde
docs: Mettre à jour documentation
style: Améliorer interface combat
```

### **Tests**
- Utiliser `test.html` pour tester les nouvelles fonctionnalités
- Vérifier la compatibilité mobile
- Tester les sauvegardes/chargements

## 🎉 Conclusion

Shinobi Chronicles dispose maintenant d'une **base technique solide** et **extensible**. Le jeu est **jouable** avec :

- ✅ **Exploration** fonctionnelle avec fog of war
- ✅ **Combat** équilibré avec jutsu et éléments
- ✅ **Progression** avec clans et niveaux
- ✅ **Sauvegarde** robuste et sécurisée
- ✅ **Interface** responsive et intuitive

La prochaine étape est d'ajouter du **contenu** (quêtes, donjons, histoire) et de **polir** l'expérience utilisateur.

**Que l'aventure ninja commence ! 🥷✨**
