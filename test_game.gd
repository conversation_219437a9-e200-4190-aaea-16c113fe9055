extends Node

# Script de test pour Shinobi Chronicles

func _ready():
	print("=== Test Shinobi Chronicles ===")
	
	# Test GameConfig
	test_game_config()
	
	# Test GameState
	test_game_state()
	
	# Test CombatSystem
	test_combat_system()
	
	# Test DataLoader
	test_data_loader()
	
	print("=== Tests terminés ===")

func test_game_config():
	print("\n--- Test GameConfig ---")
	
	# Test des constantes
	print("Version du jeu: ", GameConfig.GAME_VERSION)
	print("Taille des tuiles: ", GameConfig.WORLD_CONFIG.tile_size)
	print("Chakra de base: ", GameConfig.CHAKRA_CONFIG.base_amount)
	
	# Test des relations d'éléments
	var fire_vs_wind = GameConfig.get_element_effectiveness("fire", "wind")
	var water_vs_fire = GameConfig.get_element_effectiveness("water", "fire")
	print("<PERSON><PERSON> vs Vent: ", fire_vs_wind)
	print("<PERSON><PERSON> vs <PERSON><PERSON>: ", water_vs_fire)
	
	# Test des données de clan
	var ryujin_data = GameConfig.get_clan_data("ryujin")
	print("Clan Ryūjin: ", ryujin_data.name)
	
	print("✅ GameConfig OK")

func test_game_state():
	print("\n--- Test GameState ---")
	
	var game_state = GameState.new()
	add_child(game_state)
	
	# Test des données initiales
	print("Nom du joueur: ", game_state.player_data.name)
	print("Clan: ", game_state.player_data.clan)
	print("Niveau: ", game_state.player_data.level)
	print("HP: ", game_state.player_data.hp, "/", game_state.player_data.max_hp)
	print("Chakra: ", game_state.player_data.chakra, "/", game_state.player_data.max_chakra)
	
	# Test utilisation de chakra
	var chakra_before = game_state.player_data.chakra
	var success = game_state.use_chakra(5)
	print("Utilisation de 5 chakra: ", success)
	print("Chakra avant: ", chakra_before, " après: ", game_state.player_data.chakra)
	
	# Test gain d'XP
	var level_before = game_state.player_data.level
	game_state.gain_xp(150)
	print("Niveau avant XP: ", level_before, " après: ", game_state.player_data.level)
	
	# Test ajout d'objet
	game_state.add_item("chakra_pill", 3)
	print("Inventaire: ", game_state.inventory)
	
	game_state.queue_free()
	print("✅ GameState OK")

func test_combat_system():
	print("\n--- Test CombatSystem ---")
	
	var combat_system = CombatSystem.new()
	var game_state = GameState.new()
	add_child(combat_system)
	add_child(game_state)
	
	# Test des jutsu disponibles
	var available_jutsu = combat_system.get_available_jutsu(game_state)
	print("Jutsu disponibles: ", available_jutsu.size())
	for jutsu in available_jutsu:
		print("  - ", jutsu.data.name, " (", jutsu.data.chakra_cost, " chakra)")
	
	# Test de démarrage de combat
	print("Test de combat contre wild_boar...")
	combat_system.start_combat("wild_boar", game_state)
	
	if not combat_system.current_enemy.is_empty():
		print("Ennemi: ", combat_system.current_enemy.name)
		print("HP ennemi: ", combat_system.current_enemy.hp)
		print("État du combat: ", combat_system.current_state)
	else:
		print("❌ Erreur: Ennemi non trouvé")
	
	combat_system.queue_free()
	game_state.queue_free()
	print("✅ CombatSystem OK")

func test_data_loader():
	print("\n--- Test DataLoader ---")
	
	var data_loader = DataLoader.new()
	add_child(data_loader)
	
	# Attendre que les données soient chargées
	await data_loader.data_loaded
	
	# Test des ennemis
	var enemies = data_loader.get_all_enemies()
	print("Ennemis chargés: ", enemies.size())
	
	var wild_boar = data_loader.get_enemy_data("wild_boar")
	if not wild_boar.is_empty():
		print("Sanglier: ", wild_boar.name, " (Niv. ", wild_boar.level, ")")
	
	# Test des jutsu
	var jutsu = data_loader.get_all_jutsu()
	print("Jutsu chargés: ", jutsu.size())
	
	var basic_attack = data_loader.get_jutsu_data("basic_attack")
	if not basic_attack.is_empty():
		print("Attaque de base: ", basic_attack.name)
	
	# Test des objets
	var items = data_loader.get_all_items()
	print("Objets chargés: ", items.size())
	
	var chakra_pill = data_loader.get_item_data("chakra_pill")
	if not chakra_pill.is_empty():
		print("Pilule de chakra: ", chakra_pill.name)
	
	# Test de validation
	var is_valid = data_loader.validate_data()
	print("Données valides: ", is_valid)
	
	data_loader.queue_free()
	print("✅ DataLoader OK")

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		get_tree().quit()
