extends Node

# Script de test simple pour Shinobi Chronicles

func _ready():
	print("🥷 === Test Shinobi Chronicles === 🥷")

	# Test de base
	test_basic_functionality()

	print("=== Tests terminés ===")
	print("Appuyez sur Échap pour quitter")

func test_basic_functionality():
	print("\n--- Test de Base ---")

	# Test simple sans dépendances
	print("Godot Engine fonctionne !")
	print("Version du projet: 1.0.0")

	# Test de création d'objets simples
	var test_dict = {
		"name": "Test Ninja",
		"level": 1,
		"hp": 100
	}

	print("Données de test: ", test_dict)
	print("✅ Tests de base OK")

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		print("Fermeture du test...")
		get_tree().quit()


