{"wild_boar": {"name": "Sanglier Sauvage", "level": 2, "hp": 40, "max_hp": 40, "attack": 8, "defense": 3, "speed": 5, "element": "earth", "xp_reward": 25, "ryo_reward": 15, "loot": [{"id": "boar_tusk", "chance": 0.3, "quantity": 1}], "ai_pattern": "aggressive", "description": "Un sanglier sauvage agressif qui charge ses ennemis."}, "forest_wolf": {"name": "<PERSON><PERSON>", "level": 4, "hp": 60, "max_hp": 60, "attack": 12, "defense": 4, "speed": 8, "element": "wind", "xp_reward": 40, "ryo_reward": 25, "loot": [{"id": "wolf_fang", "chance": 0.4, "quantity": 1}, {"id": "wolf_pelt", "chance": 0.2, "quantity": 1}], "ai_pattern": "hit_and_run", "description": "Un loup agile qui utilise sa vitesse pour attaquer et esquiver."}, "shadow_ninja": {"name": "Ninja de l'Ombre", "level": 6, "hp": 80, "max_hp": 80, "attack": 15, "defense": 6, "speed": 12, "element": "shadow", "xp_reward": 75, "ryo_reward": 50, "loot": [{"id": "shadow_scroll", "chance": 0.2, "quantity": 1}, {"id": "ninja_star", "chance": 0.5, "quantity": 2}], "ai_pattern": "tactical", "description": "Un ninja renégat maîtrisant les techniques d'ombre."}, "fire_salamander": {"name": "<PERSON><PERSON><PERSON>", "level": 5, "hp": 70, "max_hp": 70, "attack": 14, "defense": 5, "speed": 6, "element": "fire", "xp_reward": 60, "ryo_reward": 35, "loot": [{"id": "fire_crystal", "chance": 0.3, "quantity": 1}, {"id": "salamander_scale", "chance": 0.4, "quantity": 1}], "ai_pattern": "aggressive", "description": "Une salamandre géante crachant des flammes."}, "water_spirit": {"name": "Esprit <PERSON> l'Eau", "level": 7, "hp": 90, "max_hp": 90, "attack": 16, "defense": 8, "speed": 10, "element": "water", "xp_reward": 85, "ryo_reward": 60, "loot": [{"id": "water_essence", "chance": 0.25, "quantity": 1}, {"id": "spirit_orb", "chance": 0.15, "quantity": 1}], "ai_pattern": "tactical", "description": "Un esprit élémentaire de l'eau, sage mais dangereux."}, "earth_golem": {"name": "<PERSON><PERSON> de <PERSON>", "level": 8, "hp": 120, "max_hp": 120, "attack": 18, "defense": 12, "speed": 4, "element": "earth", "xp_reward": 100, "ryo_reward": 70, "loot": [{"id": "earth_core", "chance": 0.2, "quantity": 1}, {"id": "stone_fragment", "chance": 0.6, "quantity": 3}], "ai_pattern": "defensive", "description": "Un golem massif fait de pierre et de terre."}, "wind_hawk": {"name": "Faucon du Vent", "level": 6, "hp": 65, "max_hp": 65, "attack": 13, "defense": 4, "speed": 15, "element": "wind", "xp_reward": 70, "ryo_reward": 45, "loot": [{"id": "wind_feather", "chance": 0.4, "quantity": 2}, {"id": "hawk_talon", "chance": 0.3, "quantity": 1}], "ai_pattern": "hit_and_run", "description": "Un faucon géant maîtrisant les courants d'air."}, "shadow_beast": {"name": "Bête de l'Ombre", "level": 9, "hp": 110, "max_hp": 110, "attack": 20, "defense": 7, "speed": 14, "element": "shadow", "xp_reward": 120, "ryo_reward": 80, "loot": [{"id": "shadow_essence", "chance": 0.3, "quantity": 1}, {"id": "dark_crystal", "chance": 0.2, "quantity": 1}], "ai_pattern": "berserker", "description": "Une créature corrompue par les ombres, extrêmement agressive."}, "mountain_troll": {"name": "Troll des Montagnes", "level": 10, "hp": 150, "max_hp": 150, "attack": 22, "defense": 10, "speed": 6, "element": "earth", "xp_reward": 140, "ryo_reward": 90, "loot": [{"id": "troll_hide", "chance": 0.4, "quantity": 1}, {"id": "mountain_stone", "chance": 0.5, "quantity": 2}], "ai_pattern": "aggressive", "description": "Un troll massif gardien des montagnes."}, "elemental_master": {"name": "Maître Élémentaire", "level": 12, "hp": 200, "max_hp": 200, "attack": 25, "defense": 12, "speed": 16, "element": "neutral", "xp_reward": 200, "ryo_reward": 150, "loot": [{"id": "elemental_orb", "chance": 0.5, "quantity": 1}, {"id": "master_scroll", "chance": 0.3, "quantity": 1}, {"id": "rare_gem", "chance": 0.2, "quantity": 1}], "ai_pattern": "master", "description": "Un ninja maître capable d'utiliser tous les éléments. Boss final de la région."}}