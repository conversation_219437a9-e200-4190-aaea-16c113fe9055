extends Node

# Version avec interface forcée visible pour Shinobi Chronicles

# Données du joueur
var player_data = {
	"name": "Ninja",
	"clan": "ryujin",
	"level": 1,
	"xp": 0,
	"hp": 100,
	"max_hp": 100,
	"chakra": 20,
	"max_chakra": 20,
	"attack": 12,  # Bonus clan déjà appliqué
	"defense": 5,
	"speed": 8,
	"position": Vector2(50, 50)
}

var inventory = {}
var game_stats = {"battles_won": 0, "enemies_defeated": 0, "play_time": 0.0}

# Variables d'interface
var player_sprite: Sprite2D
var camera: Camera2D
var ui_layer: CanvasLayer
var info_panel: Panel
var message_label: Label

# Variables de jeu
var is_moving: bool = false
var chakra_regen_timer: float = 0.0
var message_timer: Timer

func _ready():
	print("🥷 Shinobi Chronicles - Version Interface Visible")
	setup_game()

func setup_game():
	# <PERSON><PERSON><PERSON> le joueur ninja
	create_ninja_player()
	
	# Créer l'interface FORCÉE
	create_forced_ui()
	
	# Configurer les systèmes
	set_process_input(true)
	set_process(true)
	
	print("✅ Jeu avec interface forcée initialisé!")
	show_message("🥷 Bienvenue! Interface ninja activée!")

func create_ninja_player():
	# Sprite ninja
	player_sprite = Sprite2D.new()
	player_sprite.name = "Ninja"
	
	# Texture ninja rouge avec détails
	var image = Image.create(32, 32, false, Image.FORMAT_RGB8)
	for x in range(32):
		for y in range(32):
			if x == 0 or x == 31 or y == 0 or y == 31:
				image.set_pixel(x, y, Color.BLACK)
			elif y < 10:
				image.set_pixel(x, y, Color(0.3, 0.1, 0.1))
			elif (x == 10 or x == 22) and y == 8:
				image.set_pixel(x, y, Color.YELLOW)
			else:
				image.set_pixel(x, y, Color.RED)
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	player_sprite.texture = texture
	player_sprite.position = player_data.position * 32
	
	add_child(player_sprite)
	
	# Caméra
	camera = Camera2D.new()
	camera.enabled = true
	player_sprite.add_child(camera)

func create_forced_ui():
	# Créer un CanvasLayer pour forcer l'affichage au-dessus de tout
	ui_layer = CanvasLayer.new()
	ui_layer.name = "ForceUI"
	ui_layer.layer = 100  # Au-dessus de tout
	add_child(ui_layer)
	
	# Panel principal FORCÉ visible
	info_panel = Panel.new()
	info_panel.position = Vector2(20, 20)
	info_panel.size = Vector2(400, 300)
	info_panel.modulate = Color(0.2, 0.2, 0.2, 0.9)  # Fond sombre semi-transparent
	ui_layer.add_child(info_panel)
	
	# Titre FORCÉ
	var title = Label.new()
	title.text = "🥷 SHINOBI CHRONICLES 🥷"
	title.position = Vector2(10, 10)
	title.size = Vector2(380, 30)
	title.add_theme_color_override("font_color", Color.GOLD)
	title.add_theme_font_size_override("font_size", 18)
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	info_panel.add_child(title)
	
	# Stats FORCÉES visibles
	var stats_text = "Niveau: " + str(player_data.level) + "\n"
	stats_text += "HP: " + str(player_data.hp) + "/" + str(player_data.max_hp) + "\n"
	stats_text += "Chakra: " + str(player_data.chakra) + "/" + str(player_data.max_chakra) + "\n"
	stats_text += "Attaque: " + str(player_data.attack) + "\n"
	stats_text += "Défense: " + str(player_data.defense) + "\n"
	stats_text += "Vitesse: " + str(player_data.speed) + "\n"
	stats_text += "Position: " + str(player_data.position) + "\n\n"
	stats_text += "Clan: Ryūjin (Maîtres du Feu)\n\n"
	stats_text += "🎮 CONTRÔLES:\n"
	stats_text += "WASD: Bouger (1 chakra)\n"
	stats_text += "F: Scanner (5 chakra)\n"
	stats_text += "E: Fouiller\n"
	stats_text += "Échap: Menu"
	
	var stats_label = Label.new()
	stats_label.text = stats_text
	stats_label.position = Vector2(10, 50)
	stats_label.size = Vector2(380, 240)
	stats_label.add_theme_color_override("font_color", Color.WHITE)
	stats_label.add_theme_font_size_override("font_size", 14)
	stats_label.vertical_alignment = VERTICAL_ALIGNMENT_TOP
	info_panel.add_child(stats_label)
	
	# Messages FORCÉS
	message_label = Label.new()
	message_label.position = Vector2(20, 340)
	message_label.size = Vector2(600, 60)
	message_label.add_theme_color_override("font_color", Color.YELLOW)
	message_label.add_theme_color_override("font_shadow_color", Color.BLACK)
	message_label.add_theme_font_size_override("font_size", 16)
	message_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	message_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	ui_layer.add_child(message_label)
	
	# Instructions FORCÉES
	var instructions = Label.new()
	instructions.text = "🥷 NINJA PRÊT! Utilisez WASD pour bouger, F pour scanner, E pour fouiller!"
	instructions.position = Vector2(20, 420)
	instructions.size = Vector2(600, 40)
	instructions.add_theme_color_override("font_color", Color.CYAN)
	instructions.add_theme_font_size_override("font_size", 14)
	instructions.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	ui_layer.add_child(instructions)
	
	# Timer pour messages
	message_timer = Timer.new()
	message_timer.wait_time = 3.0
	message_timer.one_shot = true
	message_timer.timeout.connect(_on_message_timeout)
	add_child(message_timer)
	
	print("✅ Interface FORCÉE créée et visible!")

func _process(delta):
	# Régénération de chakra
	chakra_regen_timer += delta
	if chakra_regen_timer >= 8.0:
		if player_data.chakra < player_data.max_chakra:
			player_data.chakra += 1
			update_ui()
			print("💨 Chakra régénéré: ", player_data.chakra)
		chakra_regen_timer = 0.0
	
	game_stats.play_time += delta

func _input(_event):
	if is_moving:
		return
	
	var movement = Vector2.ZERO
	
	if Input.is_action_pressed("move_up"):
		movement.y = -1
	elif Input.is_action_pressed("move_down"):
		movement.y = 1
	elif Input.is_action_pressed("move_left"):
		movement.x = -1
	elif Input.is_action_pressed("move_right"):
		movement.x = 1
	
	if movement != Vector2.ZERO:
		move_ninja(movement)
	
	if Input.is_action_just_pressed("scan"):
		scan_area()
	elif Input.is_action_just_pressed("interact"):
		search_area()
	elif Input.is_action_just_pressed("ui_cancel"):
		show_menu()

func move_ninja(direction: Vector2):
	var new_pos = player_data.position + direction
	
	# Limites
	if new_pos.x < 0 or new_pos.x >= 100 or new_pos.y < 0 or new_pos.y >= 100:
		show_message("🚫 Limite du territoire ninja!")
		return
	
	# Chakra
	if player_data.chakra < 1:
		show_message("💨 Pas assez de chakra!")
		return
	
	player_data.chakra -= 1
	
	# Mouvement
	is_moving = true
	var new_pixel_pos = new_pos * 32
	
	var tween = create_tween()
	tween.tween_property(player_sprite, "position", new_pixel_pos, 0.2)
	await tween.finished
	
	player_data.position = new_pos
	
	# Chance de combat
	if randf() < 0.15:
		trigger_combat()
	
	update_ui()
	is_moving = false

func scan_area():
	if player_data.chakra < 5:
		show_message("💨 Pas assez de chakra pour scanner!")
		return
	
	player_data.chakra -= 5
	show_message("👁️ Zone scannée! Rayon de 4 tuiles révélé.")
	update_ui()

func search_area():
	show_message("🔍 Fouille en cours...")
	
	if randf() < 0.3:
		var items = ["chakra_pill", "health_potion", "ninja_star"]
		var item = items[randi() % items.size()]
		add_item(item, 1)
		show_message("🎁 Trouvé: " + get_item_name(item) + "!")
	else:
		show_message("🍃 Rien d'intéressant...")

func trigger_combat():
	var enemies = ["Sanglier", "Loup", "Ninja Renégat"]
	var enemy = enemies[randi() % enemies.size()]
	
	show_message("⚔️ Combat contre " + enemy + "!")
	
	# Combat simple
	if randf() < 0.7:
		var xp = 30 + randi() % 20
		player_data.xp += xp
		game_stats.battles_won += 1
		show_message("🏆 Victoire! +" + str(xp) + " XP")
		check_level_up()
	else:
		var damage = 10 + randi() % 10
		player_data.hp = max(5, player_data.hp - damage)
		show_message("💥 Défaite... -" + str(damage) + " HP")
	
	update_ui()

func check_level_up():
	var xp_needed = 100 * player_data.level
	if player_data.xp >= xp_needed:
		player_data.level += 1
		player_data.xp = 0
		player_data.max_hp += 20
		player_data.hp = player_data.max_hp
		player_data.max_chakra += 5
		player_data.chakra = player_data.max_chakra
		player_data.attack += 2
		show_message("🎉 NIVEAU " + str(player_data.level) + "! Stats améliorées!")
		update_ui()

func add_item(item_id: String, quantity: int):
	if inventory.has(item_id):
		inventory[item_id] += quantity
	else:
		inventory[item_id] = quantity

func get_item_name(item_id: String) -> String:
	match item_id:
		"chakra_pill": return "💊 Pilule de Chakra"
		"health_potion": return "🧪 Potion de Santé"
		"ninja_star": return "⭐ Shuriken"
		_: return item_id

func update_ui():
	# Mettre à jour le texte des stats
	var stats_text = "Niveau: " + str(player_data.level) + "\n"
	stats_text += "HP: " + str(player_data.hp) + "/" + str(player_data.max_hp) + "\n"
	stats_text += "Chakra: " + str(player_data.chakra) + "/" + str(player_data.max_chakra) + "\n"
	stats_text += "Attaque: " + str(player_data.attack) + "\n"
	stats_text += "Défense: " + str(player_data.defense) + "\n"
	stats_text += "Vitesse: " + str(player_data.speed) + "\n"
	stats_text += "Position: " + str(player_data.position) + "\n\n"
	stats_text += "Clan: Ryūjin (Maîtres du Feu)\n\n"
	
	if not inventory.is_empty():
		stats_text += "🎒 INVENTAIRE:\n"
		for item_id in inventory.keys():
			stats_text += get_item_name(item_id) + " x" + str(inventory[item_id]) + "\n"
		stats_text += "\n"
	
	stats_text += "🎮 CONTRÔLES:\n"
	stats_text += "WASD: Bouger (1 chakra)\n"
	stats_text += "F: Scanner (5 chakra)\n"
	stats_text += "E: Fouiller\n"
	stats_text += "Échap: Menu"
	
	# Trouver le label des stats et le mettre à jour
	if info_panel and info_panel.get_child_count() > 1:
		var stats_label = info_panel.get_child(1)
		if stats_label is Label:
			stats_label.text = stats_text

func show_message(text: String):
	print("🥷 ", text)
	if message_label:
		message_label.text = text
		message_label.visible = true
		message_timer.start()

func _on_message_timeout():
	if message_label:
		message_label.visible = false

func show_menu():
	var menu = AcceptDialog.new()
	menu.title = "🥷 Menu Ninja"
	menu.size = Vector2(400, 300)
	
	var vbox = VBoxContainer.new()
	menu.add_child(vbox)
	
	var info = Label.new()
	info.text = "🥷 " + player_data.name + " - Niveau " + str(player_data.level) + "\n"
	info.text += "HP: " + str(player_data.hp) + "/" + str(player_data.max_hp) + "\n"
	info.text += "Chakra: " + str(player_data.chakra) + "/" + str(player_data.max_chakra) + "\n"
	info.text += "Combats gagnés: " + str(game_stats.battles_won) + "\n"
	info.text += "Temps de jeu: " + str(int(game_stats.play_time)) + "s"
	vbox.add_child(info)
	
	var continue_btn = Button.new()
	continue_btn.text = "Continuer"
	continue_btn.pressed.connect(menu.queue_free)
	vbox.add_child(continue_btn)
	
	var heal_btn = Button.new()
	heal_btn.text = "Méditation (Restaurer)"
	heal_btn.pressed.connect(_on_heal.bind(menu))
	vbox.add_child(heal_btn)
	
	var quit_btn = Button.new()
	quit_btn.text = "Quitter"
	quit_btn.pressed.connect(get_tree().quit)
	vbox.add_child(quit_btn)
	
	add_child(menu)
	menu.popup_centered()

func _on_heal(menu: AcceptDialog):
	player_data.hp = player_data.max_hp
	player_data.chakra = player_data.max_chakra
	show_message("🧘 Méditation terminée! Tout restauré!")
	update_ui()
	menu.queue_free()
