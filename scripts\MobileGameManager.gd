extends Node

# Gestionnaire principal pour le jeu mobile Shinobi Chronicles

# États du jeu
const MAIN_MENU = 0
const WORLD_MAP = 1
const VILLAGE = 2
const BATTLE = 3
const INVENTORY = 4
const SETTINGS = 5

var current_state = MAIN_MENU
var current_scene: Node

# Données de jeu
var player_data = {
	"name": "Ninja",
	"clan": "ryujin",
	"level": 1,
	"xp": 0,
	"hp": 100,
	"max_hp": 100,
	"chakra": 20,
	"max_chakra": 20,
	"attack": 12,
	"defense": 5,
	"speed": 8,
	"position": Vector2(5, 5),  # Position sur la carte du monde
	"current_village": "konoha"
}

var inventory = {}
var game_progress = {
	"villages_unlocked": ["konoha"],
	"quests_completed": [],
	"battles_won": 0
}

# Signaux
signal state_changed(new_state)
signal player_data_updated

func _ready():
	print("🎮 Mobile Game Manager initialisé")
	
	# Configurer pour mobile
	setup_mobile_settings()
	
	# D<PERSON>marrer avec le menu principal
	change_state(MAIN_MENU)

func setup_mobile_settings():
	# Configuration mobile
	get_window().content_scale_mode = Window.CONTENT_SCALE_MODE_CANVAS_ITEMS
	get_window().content_scale_aspect = Window.CONTENT_SCALE_ASPECT_KEEP
	
	# Résolution mobile de base
	get_window().size = Vector2i(720, 1280)  # Format mobile portrait

func change_state(new_state: int):
	var state_names = ["MAIN_MENU", "WORLD_MAP", "VILLAGE", "BATTLE", "INVENTORY", "SETTINGS"]
	print("🔄 Changement d'état: ", state_names[current_state], " -> ", state_names[new_state])

	# Nettoyer l'ancienne scène
	if current_scene:
		current_scene.queue_free()
		await current_scene.tree_exited

	current_state = new_state

	# Créer la nouvelle scène
	match new_state:
		MAIN_MENU:
			load_main_menu()
		WORLD_MAP:
			load_world_map()
		VILLAGE:
			load_village()
		BATTLE:
			load_battle()
		INVENTORY:
			load_inventory()
		SETTINGS:
			load_settings()

	emit_signal("state_changed", new_state)

func load_main_menu():
	var main_menu_script = preload("res://scripts/MainMenu.gd")
	current_scene = main_menu_script.new()
	current_scene.name = "MainMenu"
	
	# Connecter les signaux
	current_scene.start_game.connect(_on_start_game)
	current_scene.quit_game.connect(_on_quit_game)
	
	add_child(current_scene)

func load_world_map():
	var world_map_script = preload("res://scripts/WorldMap.gd")
	current_scene = world_map_script.new()
	current_scene.name = "WorldMap"

	# Initialiser avec les données du joueur
	current_scene.call_deferred("initialize", player_data, game_progress)

	# Connecter les signaux
	current_scene.village_selected.connect(_on_village_selected)
	current_scene.battle_triggered.connect(_on_battle_triggered)
	current_scene.menu_requested.connect(_on_menu_requested)

	add_child(current_scene)

func load_village():
	var village_script = preload("res://scripts/Village.gd")
	current_scene = village_script.new()
	current_scene.name = "Village"

	# Initialiser avec les données du joueur
	current_scene.call_deferred("initialize", player_data, player_data.current_village)

	# Connecter les signaux
	current_scene.leave_village.connect(_on_leave_village)
	current_scene.shop_entered.connect(_on_shop_entered)
	current_scene.training_started.connect(_on_training_started)

	add_child(current_scene)

func load_battle():
	var battle_script = preload("res://scripts/MobileBattle.gd")
	current_scene = battle_script.new()
	current_scene.name = "Battle"

	# Initialiser avec les données de combat
	var enemy_data = {"name": "Ennemi", "hp": 50, "attack": 10, "level": 1}
	current_scene.call_deferred("initialize", player_data, enemy_data)

	# Connecter les signaux
	current_scene.battle_ended.connect(_on_battle_ended)

	add_child(current_scene)

func load_inventory():
	var inventory_script = preload("res://scripts/MobileInventory.gd")
	current_scene = inventory_script.new()
	current_scene.name = "Inventory"

	# Initialiser avec les données
	current_scene.call_deferred("initialize", player_data, inventory)

	# Connecter les signaux
	current_scene.item_used.connect(_on_item_used)
	current_scene.close_inventory.connect(_on_close_inventory)

	add_child(current_scene)

func load_settings():
	# Settings simple pour l'instant
	change_state(MAIN_MENU)

# Callbacks des signaux
func _on_start_game(clan: String, name: String):
	player_data.clan = clan
	player_data.name = name
	
	# Appliquer les bonus de clan
	apply_clan_bonuses(clan)
	
	print("🎮 Démarrage du jeu - Clan: ", clan, " Nom: ", name)
	change_state(WORLD_MAP)

func _on_quit_game():
	print("👋 Fermeture du jeu")
	get_tree().quit()

func _on_village_selected(village_id: String):
	player_data.current_village = village_id
	change_state(VILLAGE)

func _on_battle_triggered(enemy_data: Dictionary):
	# Sauvegarder les données de l'ennemi pour le combat
	current_scene.enemy_data = enemy_data
	change_state(BATTLE)

func _on_menu_requested():
	change_state(MAIN_MENU)

func _on_leave_village():
	change_state(WORLD_MAP)

func _on_shop_entered():
	print("🏪 Entrée dans la boutique")
	# Implémenter la boutique plus tard

func _on_training_started():
	print("🥋 Entraînement commencé")
	# Donner de l'XP
	gain_xp(50)

func _on_battle_ended(victory: bool, rewards: Dictionary):
	if victory:
		print("🏆 Victoire!")
		game_progress.battles_won += 1
		
		if rewards.has("xp"):
			gain_xp(rewards.xp)
		if rewards.has("items"):
			for item in rewards.items:
				add_item(item.id, item.quantity)
	else:
		print("💀 Défaite...")
		# Pénalité de défaite
		player_data.hp = max(1, player_data.hp - 20)
	
	# Retourner à la carte du monde
	change_state(WORLD_MAP)

func _on_item_used(item_id: String):
	use_item(item_id)

func _on_close_inventory():
	change_state(WORLD_MAP)

# Fonctions de gameplay
func apply_clan_bonuses(clan: String):
	match clan:
		"ryujin":
			player_data.attack += 2
			print("🔥 Bonus Clan Ryūjin: +2 Attaque")
		"yurei":
			player_data.speed += 2
			print("👤 Bonus Clan Yūrei: +2 Vitesse")
		"kaze":
			player_data.speed += 1
			player_data.max_chakra += 5
			player_data.chakra += 5
			print("💨 Bonus Clan Kaze: +1 Vitesse, +5 Chakra")

func gain_xp(amount: int):
	player_data.xp += amount
	print("⭐ +", amount, " XP (Total: ", player_data.xp, ")")
	
	# Vérifier level up
	var xp_needed = get_xp_for_level(player_data.level + 1)
	if player_data.xp >= xp_needed:
		level_up()
	
	emit_signal("player_data_updated")

func level_up():
	player_data.level += 1
	
	# Augmenter les stats
	var hp_gain = 15 + randi() % 10
	var chakra_gain = 3 + randi() % 5
	var attack_gain = 1 + randi() % 3
	var defense_gain = 1 + randi() % 2
	
	player_data.max_hp += hp_gain
	player_data.hp = player_data.max_hp  # Heal complet
	player_data.max_chakra += chakra_gain
	player_data.chakra = player_data.max_chakra
	player_data.attack += attack_gain
	player_data.defense += defense_gain
	
	print("🎉 NIVEAU ", player_data.level, "!")
	print("  ❤️ HP: +", hp_gain, " (", player_data.max_hp, ")")
	print("  🔵 Chakra: +", chakra_gain, " (", player_data.max_chakra, ")")
	print("  ⚔️ Attaque: +", attack_gain, " (", player_data.attack, ")")

func get_xp_for_level(level: int) -> int:
	return 100 * level

func add_item(item_id: String, quantity: int = 1):
	if inventory.has(item_id):
		inventory[item_id] += quantity
	else:
		inventory[item_id] = quantity
	
	print("🎒 +", quantity, " ", item_id)

func use_item(item_id: String) -> bool:
	if not inventory.has(item_id) or inventory[item_id] <= 0:
		return false
	
	inventory[item_id] -= 1
	if inventory[item_id] <= 0:
		inventory.erase(item_id)
	
	# Appliquer l'effet
	apply_item_effect(item_id)
	emit_signal("player_data_updated")
	return true

func apply_item_effect(item_id: String):
	match item_id:
		"health_potion":
			var heal = 30
			player_data.hp = min(player_data.hp + heal, player_data.max_hp)
			print("🧪 +", heal, " HP")
		"chakra_pill":
			var restore = 15
			player_data.chakra = min(player_data.chakra + restore, player_data.max_chakra)
			print("💊 +", restore, " Chakra")

# Sauvegarde
func save_game():
	var save_data = {
		"player_data": player_data,
		"inventory": inventory,
		"game_progress": game_progress,
		"timestamp": Time.get_unix_time_from_system()
	}
	
	var file = FileAccess.open("user://save_game.dat", FileAccess.WRITE)
	if file:
		file.store_string(JSON.stringify(save_data))
		file.close()
		print("💾 Jeu sauvegardé")
		return true
	return false

func load_game() -> bool:
	if not FileAccess.file_exists("user://save_game.dat"):
		return false
	
	var file = FileAccess.open("user://save_game.dat", FileAccess.READ)
	if file:
		var json_string = file.get_as_text()
		file.close()
		
		var json = JSON.new()
		var parse_result = json.parse(json_string)
		
		if parse_result == OK:
			var save_data = json.data
			player_data = save_data.get("player_data", player_data)
			inventory = save_data.get("inventory", {})
			game_progress = save_data.get("game_progress", game_progress)
			
			print("📂 Jeu chargé")
			emit_signal("player_data_updated")
			return true
	
	return false
