extends Node

# Version fonctionnelle de Shinobi Chronicles sans dépendances class_name

# Chargement direct des scripts
var GameStateScript = preload("res://scripts/GameStateFixed.gd")

var game_state: Node
var player_sprite: Sprite2D
var camera: Camera2D
var hud_container: Control

# UI Elements
var hp_bar: ProgressBar
var chakra_bar: ProgressBar
var xp_bar: ProgressBar
var level_label: Label
var position_label: Label
var clan_label: Label
var message_label: Label
var stats_label: Label

var is_moving: bool = false
var message_timer: Timer

func _ready():
	print("🥷 Shinobi Chronicles - Version Fonctionnelle")
	setup_game()

func setup_game():
	# Créer le GameState en utilisant le script préchargé
	game_state = GameStateScript.new()
	add_child(game_state)
	
	# Connecter les signaux
	game_state.player_stats_changed.connect(_on_player_stats_changed)
	game_state.chakra_changed.connect(_on_chakra_changed)
	game_state.level_up.connect(_on_level_up)
	game_state.item_added.connect(_on_item_added)
	
	# Créer le joueur
	setup_player()
	
	# Créer l'interface
	setup_hud()
	
	# Configurer les inputs
	set_process_input(true)
	
	print("🎮 Jeu initialisé avec succès!")
	print(game_state.get_player_summary())

func setup_player():
	# Créer le sprite du joueur avec un design ninja
	player_sprite = Sprite2D.new()
	player_sprite.name = "NinjaPlayer"
	
	# Créer une texture ninja plus détaillée
	var image = Image.create(32, 32, false, Image.FORMAT_RGB8)
	
	# Corps ninja (rouge foncé)
	image.fill(Color(0.6, 0.1, 0.1))
	
	# Ajouter des détails ninja
	for x in range(32):
		for y in range(32):
			# Bordure noire
			if x == 0 or x == 31 or y == 0 or y == 31:
				image.set_pixel(x, y, Color.BLACK)
			# Masque ninja (partie supérieure plus sombre)
			elif y < 12:
				image.set_pixel(x, y, Color(0.3, 0.05, 0.05))
			# Yeux ninja (points blancs)
			elif (x == 10 or x == 22) and y == 8:
				image.set_pixel(x, y, Color.WHITE)
			# Ceinture ninja
			elif y >= 20 and y <= 22:
				image.set_pixel(x, y, Color.BLACK)
			# Détails d'armure
			elif (x % 4 == 0 or y % 4 == 0) and x > 2 and x < 29 and y > 12 and y < 20:
				image.set_pixel(x, y, Color(0.4, 0.05, 0.05))
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	player_sprite.texture = texture
	
	add_child(player_sprite)
	
	# Position initiale
	update_player_position()
	
	# Créer la caméra
	camera = Camera2D.new()
	camera.name = "NinjaCamera"
	camera.enabled = true
	player_sprite.add_child(camera)

func update_player_position():
	var pixel_pos = game_state.player_data.position * 32
	player_sprite.position = pixel_pos

func setup_hud():
	# Container principal pour l'interface
	hud_container = Control.new()
	hud_container.name = "NinjaHUD"
	hud_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	hud_container.mouse_filter = Control.MOUSE_FILTER_IGNORE
	add_child(hud_container)
	
	# Panel d'informations principal avec style ninja
	var info_panel = Panel.new()
	info_panel.position = Vector2(10, 10)
	info_panel.size = Vector2(350, 250)
	info_panel.modulate = Color(0.1, 0.1, 0.1, 0.8)  # Fond sombre ninja
	hud_container.add_child(info_panel)
	
	var vbox = VBoxContainer.new()
	vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	vbox.add_theme_constant_override("separation", 8)
	info_panel.add_child(vbox)
	
	# Titre ninja avec style
	var title_hbox = HBoxContainer.new()
	vbox.add_child(title_hbox)
	
	var title_label = Label.new()
	title_label.text = "🥷 SHINOBI CHRONICLES 🥷"
	title_label.add_theme_color_override("font_color", Color.GOLD)
	title_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	title_hbox.add_child(title_label)
	
	level_label = Label.new()
	level_label.text = "Niv. " + str(game_state.player_data.level)
	level_label.add_theme_color_override("font_color", Color.CYAN)
	level_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
	title_hbox.add_child(level_label)
	
	# HP Bar avec style ninja
	create_stat_bar(vbox, "❤️ VIE", game_state.player_data.hp, game_state.player_data.max_hp, Color.RED)
	hp_bar = vbox.get_child(vbox.get_child_count() - 1).get_child(1)
	
	# Chakra Bar avec style ninja
	create_stat_bar(vbox, "🔵 CHAKRA", game_state.player_data.chakra, game_state.player_data.max_chakra, Color.CYAN)
	chakra_bar = vbox.get_child(vbox.get_child_count() - 1).get_child(1)
	
	# XP Bar avec style ninja
	var current_level_xp = game_state.get_xp_for_level(game_state.player_data.level)
	var next_level_xp = game_state.get_xp_for_level(game_state.player_data.level + 1)
	var xp_progress = game_state.player_data.xp - current_level_xp
	var xp_needed = next_level_xp - current_level_xp
	
	create_stat_bar(vbox, "⭐ EXPÉRIENCE", xp_progress, xp_needed, Color.YELLOW)
	xp_bar = vbox.get_child(vbox.get_child_count() - 1).get_child(1)
	
	# Informations du clan
	clan_label = Label.new()
	clan_label.text = "🏮 Clan: " + game_state.player_data.clan.capitalize()
	clan_label.add_theme_color_override("font_color", Color.ORANGE)
	clan_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(clan_label)
	
	# Position actuelle
	position_label = Label.new()
	position_label.text = "📍 Position: " + str(game_state.player_data.position)
	position_label.add_theme_color_override("font_color", Color.LIGHT_GREEN)
	position_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(position_label)
	
	# Statistiques rapides
	stats_label = Label.new()
	update_stats_display()
	stats_label.add_theme_color_override("font_color", Color.WHITE)
	stats_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(stats_label)
	
	# Zone de messages avec style ninja
	message_label = Label.new()
	message_label.position = Vector2(10, 280)
	message_label.size = Vector2(500, 60)
	message_label.add_theme_color_override("font_color", Color.YELLOW)
	message_label.add_theme_color_override("font_shadow_color", Color.BLACK)
	message_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	message_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	message_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	hud_container.add_child(message_label)
	
	# Contrôles affichés
	var controls_label = Label.new()
	controls_label.position = Vector2(get_viewport().size.x - 250, 10)
	controls_label.size = Vector2(240, 120)
	controls_label.text = "🎮 CONTRÔLES NINJA:\n\nWASD: Déplacement (1 chakra)\nF: Scan Zone (5 chakra)\nE: Fouiller\nÉchap: Menu Ninja"
	controls_label.add_theme_color_override("font_color", Color.LIGHT_GRAY)
	controls_label.modulate = Color(1, 1, 1, 0.7)
	hud_container.add_child(controls_label)
	
	# Timer pour les messages
	message_timer = Timer.new()
	message_timer.wait_time = 4.0
	message_timer.one_shot = true
	message_timer.timeout.connect(_on_message_timeout)
	add_child(message_timer)
	
	# Message de bienvenue
	show_message("🥷 Bienvenue, ninja " + game_state.player_data.clan.capitalize() + "! Votre aventure commence...")

func create_stat_bar(parent: VBoxContainer, label_text: String, current: int, maximum: int, color: Color):
	var hbox = HBoxContainer.new()
	parent.add_child(hbox)
	
	var label = Label.new()
	label.text = label_text
	label.add_theme_color_override("font_color", Color.WHITE)
	label.custom_minimum_size.x = 100
	hbox.add_child(label)
	
	var bar = ProgressBar.new()
	bar.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	bar.show_percentage = false
	bar.modulate = color
	bar.max_value = maximum
	bar.value = current
	hbox.add_child(bar)
	
	var value_label = Label.new()
	value_label.text = str(current) + "/" + str(maximum)
	value_label.add_theme_color_override("font_color", color)
	value_label.custom_minimum_size.x = 60
	hbox.add_child(value_label)

func update_stats_display():
	if stats_label:
		stats_label.text = "⚔️ Att:" + str(game_state.player_data.attack) + " 🛡️ Def:" + str(game_state.player_data.defense) + " ⚡ Vit:" + str(game_state.player_data.speed)

func _input(event):
	if is_moving:
		return
	
	var movement_vector = Vector2.ZERO
	
	# Gestion des mouvements ninja
	if Input.is_action_pressed("move_up"):
		movement_vector.y = -1
	elif Input.is_action_pressed("move_down"):
		movement_vector.y = 1
	elif Input.is_action_pressed("move_left"):
		movement_vector.x = -1
	elif Input.is_action_pressed("move_right"):
		movement_vector.x = 1
	
	if movement_vector != Vector2.ZERO:
		move_player(movement_vector)
	
	# Actions ninja spéciales
	if Input.is_action_just_pressed("scan"):
		scan_area()
	elif Input.is_action_just_pressed("interact"):
		search_area()
	elif Input.is_action_just_pressed("ui_cancel"):
		show_ninja_menu()

func move_player(direction: Vector2):
	var new_world_pos = game_state.player_data.position + direction
	
	# Vérifier les limites du monde ninja
	if new_world_pos.x < 0 or new_world_pos.x >= 100:
		show_message("🚫 Limite du territoire ninja atteinte!")
		return
	if new_world_pos.y < 0 or new_world_pos.y >= 100:
		show_message("🚫 Limite du territoire ninja atteinte!")
		return
	
	# Essayer de déplacer le ninja
	if not game_state.move_player(new_world_pos):
		show_message("💨 Chakra insuffisant pour ce déplacement ninja!")
		return
	
	# Animation de mouvement ninja
	is_moving = true
	var new_pixel_pos = new_world_pos * 32
	
	var tween = create_tween()
	tween.tween_property(player_sprite, "position", new_pixel_pos, 0.15)
	await tween.finished
	
	# Marquer la zone comme explorée
	game_state.explore_tile(new_world_pos)
	
	# Chance de rencontre ninja
	if randf() < 0.12:  # 12% de chance
		trigger_ninja_encounter()
	
	# Mettre à jour l'interface
	update_ui()
	
	is_moving = false

func scan_area():
	if not game_state.use_chakra(5):
		show_message("💨 Chakra insuffisant pour la technique de scan!")
		return
	
	# Technique ninja de reconnaissance
	var scan_radius = 4
	var player_tile = game_state.player_data.position
	
	for x in range(-scan_radius, scan_radius + 1):
		for y in range(-scan_radius, scan_radius + 1):
			var tile_pos = player_tile + Vector2(x, y)
			if tile_pos.x >= 0 and tile_pos.x < 100 and tile_pos.y >= 0 and tile_pos.y < 100:
				game_state.explore_tile(tile_pos)
	
	show_message("👁️ Technique de Scan Ninja activée! Zone révélée (Rayon: " + str(scan_radius) + ")")

func trigger_ninja_encounter():
	var enemies = [
		{"name": "🐗 Sanglier Sauvage", "xp": 25, "type": "beast"},
		{"name": "🐺 Loup de Forêt", "xp": 35, "type": "beast"},
		{"name": "🥷 Ninja Renégat", "xp": 50, "type": "ninja"},
		{"name": "👹 Démon Mineur", "xp": 40, "type": "demon"},
		{"name": "🦅 Faucon Géant", "xp": 30, "type": "beast"}
	]
	
	var enemy = enemies[randi() % enemies.size()]
	
	show_message("⚔️ COMBAT NINJA! Ennemi: " + enemy.name)
	
	# Combat ninja avec tactiques
	var victory_chance = 0.7 + (game_state.player_data.level * 0.05)
	var is_victory = randf() < victory_chance
	
	if is_victory:
		var xp_gain = enemy.xp + randi() % 15
		game_state.gain_xp(xp_gain)
		game_state.game_stats.battles_won += 1
		game_state.game_stats.enemies_defeated += 1
		
		show_message("🏆 Victoire ninja! +" + str(xp_gain) + " XP")
		
		# Loot ninja selon le type d'ennemi
		var loot_chance = randf()
		if enemy.type == "ninja" and loot_chance < 0.4:
			game_state.add_item("ninja_scroll", 1)
		elif enemy.type == "beast" and loot_chance < 0.3:
			game_state.add_item("chakra_pill", 1)
		elif loot_chance < 0.2:
			game_state.add_item("health_potion", 1)
	else:
		# Défaite - perte de HP mais pas de mort
		var damage = 10 + randi() % 10
		game_state.player_data.hp = max(1, game_state.player_data.hp - damage)
		show_message("💥 Défaite... -" + str(damage) + " HP. Fuyez ninja!")
		update_all_ui()

func search_area():
	show_message("🔍 Recherche ninja dans la zone...")
	
	# Technique ninja de fouille
	if randf() < 0.25:  # 25% de chance
		var findings = [
			{"item": "chakra_pill", "name": "💊 Pilule de Chakra"},
			{"item": "health_potion", "name": "🧪 Potion de Santé"},
			{"item": "ninja_star", "name": "⭐ Shuriken"},
			{"item": "smoke_bomb", "name": "💨 Bombe Fumigène"}
		]
		
		var finding = findings[randi() % findings.size()]
		game_state.add_item(finding.item, 1)
		show_message("🎁 Trouvaille ninja: " + finding.name + "!")
	else:
		var messages = [
			"🍃 Seules des feuilles mortes...",
			"🪨 Rien que des pierres ordinaires...",
			"🌸 Des pétales de cerisier dans le vent...",
			"🦗 Le chant des grillons résonne..."
		]
		show_message(messages[randi() % messages.size()])

func show_message(text: String):
	print("🥷 Message Ninja: ", text)
	message_label.text = text
	message_label.visible = true
	message_timer.start()

func _on_message_timeout():
	message_label.visible = false

func update_ui():
	position_label.text = "📍 Position: " + str(game_state.player_data.position)

func show_ninja_menu():
	var ninja_menu = AcceptDialog.new()
	ninja_menu.title = "🥷 MENU DU NINJA 🥷"
	ninja_menu.size = Vector2(450, 500)
	
	var scroll = ScrollContainer.new()
	ninja_menu.add_child(scroll)
	
	var vbox = VBoxContainer.new()
	scroll.add_child(vbox)
	
	# Statistiques ninja complètes
	var stats_text = "🥷 " + game_state.player_data.name + " du Clan " + game_state.player_data.clan.capitalize() + "\n\n"
	stats_text += "📊 STATISTIQUES NINJA:\n"
	stats_text += "Niveau: " + str(game_state.player_data.level) + "\n"
	stats_text += "XP: " + str(game_state.player_data.xp) + "/" + str(game_state.get_xp_for_level(game_state.player_data.level + 1)) + "\n"
	stats_text += "HP: " + str(game_state.player_data.hp) + "/" + str(game_state.player_data.max_hp) + "\n"
	stats_text += "Chakra: " + str(game_state.player_data.chakra) + "/" + str(game_state.player_data.max_chakra) + "\n"
	stats_text += "Attaque: " + str(game_state.player_data.attack) + "\n"
	stats_text += "Défense: " + str(game_state.player_data.defense) + "\n"
	stats_text += "Vitesse: " + str(game_state.player_data.speed) + "\n"
	stats_text += "Furtivité: " + str(game_state.player_data.stealth) + "\n\n"
	
	stats_text += "⚔️ EXPLOITS NINJA:\n"
	stats_text += "Combats gagnés: " + str(game_state.game_stats.battles_won) + "\n"
	stats_text += "Ennemis vaincus: " + str(game_state.game_stats.enemies_defeated) + "\n"
	stats_text += "Distance parcourue: " + str(int(game_state.game_stats.distance_traveled)) + " pas\n"
	stats_text += "Temps d'entraînement: " + str(int(game_state.game_stats.play_time)) + "s\n\n"
	
	if not game_state.inventory.is_empty():
		stats_text += "🎒 ÉQUIPEMENT NINJA:\n"
		for item_id in game_state.inventory.keys():
			var item_name = get_item_display_name(item_id)
			stats_text += "  " + item_name + " x" + str(game_state.inventory[item_id]) + "\n"
	
	var stats_label = Label.new()
	stats_label.text = stats_text
	stats_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	vbox.add_child(stats_label)
	
	var separator = HSeparator.new()
	vbox.add_child(separator)
	
	# Actions ninja
	var continue_button = Button.new()
	continue_button.text = "🏃 Continuer la Mission"
	continue_button.pressed.connect(ninja_menu.queue_free)
	vbox.add_child(continue_button)
	
	var training_button = Button.new()
	training_button.text = "🥋 Entraînement de Combat"
	training_button.pressed.connect(_on_ninja_training.bind(ninja_menu))
	vbox.add_child(training_button)
	
	var meditation_button = Button.new()
	meditation_button.text = "🧘 Méditation Ninja (Restaurer)"
	meditation_button.pressed.connect(_on_ninja_meditation.bind(ninja_menu))
	vbox.add_child(meditation_button)
	
	var quit_button = Button.new()
	quit_button.text = "🚪 Quitter le Dojo"
	quit_button.pressed.connect(get_tree().quit)
	vbox.add_child(quit_button)
	
	add_child(ninja_menu)
	ninja_menu.popup_centered()

func get_item_display_name(item_id: String) -> String:
	match item_id:
		"chakra_pill": return "💊 Pilule de Chakra"
		"health_potion": return "🧪 Potion de Santé"
		"ninja_star": return "⭐ Shuriken"
		"smoke_bomb": return "💨 Bombe Fumigène"
		"ninja_scroll": return "📜 Parchemin Ninja"
		_: return "❓ " + item_id

func _on_ninja_training(menu: AcceptDialog):
	show_message("🥋 Entraînement ninja intensif terminé!")
	game_state.gain_xp(100)
	# Petite chance d'améliorer les stats
	if randf() < 0.3:
		game_state.player_data.attack += 1
		show_message("💪 Votre technique s'améliore! +1 Attaque")
	menu.queue_free()

func _on_ninja_meditation(menu: AcceptDialog):
	game_state.player_data.hp = game_state.player_data.max_hp
	game_state.player_data.chakra = game_state.player_data.max_chakra
	show_message("🧘 Méditation ninja accomplie. Énergie vitale restaurée!")
	update_all_ui()
	menu.queue_free()

# Callbacks des signaux ninja
func _on_player_stats_changed():
	update_all_ui()

func _on_chakra_changed():
	update_all_ui()

func _on_level_up(new_level: int):
	show_message("🎉 ASCENSION NINJA! Niveau " + str(new_level) + " atteint!")
	update_all_ui()

func _on_item_added(item_id: String, quantity: int):
	var item_name = get_item_display_name(item_id)
	show_message("🎁 " + item_name + " x" + str(quantity) + " obtenu!")

func update_all_ui():
	if hp_bar:
		hp_bar.max_value = game_state.player_data.max_hp
		hp_bar.value = game_state.player_data.hp
		# Mettre à jour le label de valeur
		var hp_container = hp_bar.get_parent()
		if hp_container.get_child_count() > 2:
			hp_container.get_child(2).text = str(game_state.player_data.hp) + "/" + str(game_state.player_data.max_hp)
	
	if chakra_bar:
		chakra_bar.max_value = game_state.player_data.max_chakra
		chakra_bar.value = game_state.player_data.chakra
		# Mettre à jour le label de valeur
		var chakra_container = chakra_bar.get_parent()
		if chakra_container.get_child_count() > 2:
			chakra_container.get_child(2).text = str(game_state.player_data.chakra) + "/" + str(game_state.player_data.max_chakra)
	
	if level_label:
		level_label.text = "Niv. " + str(game_state.player_data.level)
	
	if xp_bar:
		var current_level_xp = game_state.get_xp_for_level(game_state.player_data.level)
		var next_level_xp = game_state.get_xp_for_level(game_state.player_data.level + 1)
		var xp_progress = game_state.player_data.xp - current_level_xp
		var xp_needed = next_level_xp - current_level_xp
		
		xp_bar.max_value = xp_needed
		xp_bar.value = xp_progress
		
		# Mettre à jour le label de valeur
		var xp_container = xp_bar.get_parent()
		if xp_container.get_child_count() > 2:
			xp_container.get_child(2).text = str(xp_progress) + "/" + str(xp_needed)
	
	update_stats_display()
	update_ui()
