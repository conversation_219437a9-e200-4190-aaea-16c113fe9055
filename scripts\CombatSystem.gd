extends Node

# Système de combat tour par tour pour Shinobi Chronicles
class_name CombatSystem

signal combat_started(enemy_data)
signal combat_ended(victory, rewards)
signal turn_started(is_player_turn)
signal damage_dealt(attacker, target, damage, element)
signal status_applied(target, status_type, duration)
signal jutsu_used(caster, jutsu_data)

enum CombatState {
	WAITING,
	PLAYER_TURN,
	ENEMY_TURN,
	COMBAT_END
}

var current_state = CombatState.WAITING
var current_enemy = {}
var player_status_effects = {}
var enemy_status_effects = {}
var turn_count = 0

var data_loader: DataLoader

# Données des ennemis (à charger depuis un fichier JSON plus tard)
var enemies_data = {
	"wild_boar": {
		"name": "Sanglier Sauvage",
		"level": 2,
		"hp": 40,
		"max_hp": 40,
		"attack": 8,
		"defense": 3,
		"speed": 5,
		"element": "earth",
		"xp_reward": 25,
		"ryo_reward": 15,
		"loot": [
			{"id": "boar_tusk", "chance": 0.3, "quantity": 1}
		],
		"ai_pattern": "aggressive"
	},
	"forest_wolf": {
		"name": "<PERSON><PERSON>",
		"level": 4,
		"hp": 60,
		"max_hp": 60,
		"attack": 12,
		"defense": 4,
		"speed": 8,
		"element": "wind",
		"xp_reward": 40,
		"ryo_reward": 25,
		"loot": [
			{"id": "wolf_fang", "chance": 0.4, "quantity": 1}
		],
		"ai_pattern": "hit_and_run"
	},
	"shadow_ninja": {
		"name": "Ninja de l'Ombre",
		"level": 6,
		"hp": 80,
		"max_hp": 80,
		"attack": 15,
		"defense": 6,
		"speed": 12,
		"element": "shadow",
		"xp_reward": 75,
		"ryo_reward": 50,
		"loot": [
			{"id": "shadow_scroll", "chance": 0.2, "quantity": 1}
		],
		"ai_pattern": "tactical"
	}
}

# Données des jutsu (à charger depuis un fichier JSON plus tard)
var jutsu_data = {
	"dragon_slash": {
		"name": "Tranche du Dragon",
		"type": "ninjutsu",
		"element": "fire",
		"chakra_cost": 15,
		"base_damage": 25,
		"status_effect": "burn",
		"status_duration": 2,
		"clan_requirement": "ryujin"
	},
	"shadow_clone": {
		"name": "Clone d'Ombre",
		"type": "ninjutsu",
		"element": "shadow",
		"chakra_cost": 20,
		"base_damage": 20,
		"status_effect": "confusion",
		"status_duration": 3,
		"clan_requirement": "yurei"
	},
	"wind_blade": {
		"name": "Lame de Vent",
		"type": "ninjutsu",
		"element": "wind",
		"chakra_cost": 12,
		"base_damage": 22,
		"status_effect": "speed_down",
		"status_duration": 2,
		"clan_requirement": "kaze"
	},
	"basic_attack": {
		"name": "Attaque de Base",
		"type": "taijutsu",
		"element": "neutral",
		"chakra_cost": 0,
		"base_damage": 15,
		"status_effect": null,
		"status_duration": 0,
		"clan_requirement": null
	}
}

# Initialiser avec le DataLoader
func initialize_with_data_loader(loader: DataLoader):
	data_loader = loader

# Démarrer un combat
func start_combat(enemy_id: String, game_state: GameState):
	var enemy_data = {}

	if data_loader:
		enemy_data = data_loader.get_enemy_data(enemy_id)
	else:
		enemy_data = enemies_data.get(enemy_id, {})

	if enemy_data.is_empty():
		print("Ennemi inconnu: ", enemy_id)
		return

	current_enemy = enemy_data.duplicate(true)
	current_state = CombatState.PLAYER_TURN
	turn_count = 0
	player_status_effects.clear()
	enemy_status_effects.clear()
	
	emit_signal("combat_started", current_enemy)
	emit_signal("turn_started", true)

# Action du joueur - Attaque de base
func player_basic_attack(game_state: GameState):
	if current_state != CombatState.PLAYER_TURN:
		return

	var jutsu = {}
	if data_loader:
		jutsu = data_loader.get_jutsu_data("basic_attack")
	else:
		jutsu = jutsu_data.get("basic_attack", {})

	execute_jutsu(game_state.player_data, current_enemy, jutsu, game_state)

	end_player_turn(game_state)

# Action du joueur - Utiliser un jutsu
func player_use_jutsu(jutsu_id: String, game_state: GameState):
	if current_state != CombatState.PLAYER_TURN:
		return

	var jutsu = {}
	if data_loader:
		jutsu = data_loader.get_jutsu_data(jutsu_id)
	else:
		jutsu = jutsu_data.get(jutsu_id, {})

	if jutsu.is_empty():
		print("Jutsu inconnu: ", jutsu_id)
		return
	
	# Vérifier les prérequis
	if jutsu.clan_requirement and jutsu.clan_requirement != game_state.player_data.clan:
		print("Ce jutsu nécessite le clan: ", jutsu.clan_requirement)
		return
	
	# Vérifier le chakra
	if not game_state.use_chakra(jutsu.chakra_cost):
		print("Pas assez de chakra")
		return
	
	execute_jutsu(game_state.player_data, current_enemy, jutsu, game_state)
	emit_signal("jutsu_used", "player", jutsu)
	
	end_player_turn(game_state)

# Exécuter un jutsu
func execute_jutsu(attacker: Dictionary, target: Dictionary, jutsu: Dictionary, game_state: GameState):
	var damage = calculate_damage(attacker, target, jutsu)
	
	target.hp -= damage
	target.hp = max(0, target.hp)
	
	emit_signal("damage_dealt", attacker, target, damage, jutsu.element)
	
	# Appliquer l'effet de statut
	if jutsu.status_effect and jutsu.status_duration > 0:
		apply_status_effect(target, jutsu.status_effect, jutsu.status_duration)

# Calculer les dégâts
func calculate_damage(attacker: Dictionary, target: Dictionary, jutsu: Dictionary) -> int:
	var base_damage = jutsu.base_damage + attacker.attack
	
	# Réduction par la défense
	var defense_reduction = target.defense * 0.5
	base_damage = max(1, base_damage - defense_reduction)
	
	# Multiplicateur d'élément
	var element_multiplier = GameConfig.get_element_effectiveness(jutsu.element, target.get("element", "neutral"))
	base_damage *= element_multiplier
	
	# Bonus de clan
	if attacker.has("clan"):
		var clan_data = GameConfig.get_clan_data(attacker.clan)
		if clan_data.has("bonus_attack"):
			base_damage += clan_data.bonus_attack
	
	# Variation aléatoire (±10%)
	var variation = randf_range(0.9, 1.1)
	base_damage *= variation
	
	return int(base_damage)

# Appliquer un effet de statut
func apply_status_effect(target: Dictionary, effect_type: String, duration: int):
	var status_effects = enemy_status_effects if target == current_enemy else player_status_effects
	
	status_effects[effect_type] = duration
	emit_signal("status_applied", target, effect_type, duration)

# Finir le tour du joueur
func end_player_turn(game_state: GameState):
	# Vérifier si l'ennemi est vaincu
	if current_enemy.hp <= 0:
		end_combat(true, game_state)
		return
	
	# Tour de l'ennemi
	current_state = CombatState.ENEMY_TURN
	emit_signal("turn_started", false)
	
	# Délai avant l'action de l'ennemi
	await get_tree().create_timer(1.0).timeout
	enemy_turn(game_state)

# Tour de l'ennemi
func enemy_turn(game_state: GameState):
	if current_state != CombatState.ENEMY_TURN:
		return
	
	# IA de l'ennemi selon son pattern
	match current_enemy.get("ai_pattern", "aggressive"):
		"aggressive":
			enemy_aggressive_ai(game_state)
		"hit_and_run":
			enemy_hit_and_run_ai(game_state)
		"tactical":
			enemy_tactical_ai(game_state)
		_:
			enemy_aggressive_ai(game_state)
	
	# Vérifier si le joueur est vaincu
	if game_state.player_data.hp <= 0:
		end_combat(false, game_state)
		return
	
	# Traiter les effets de statut
	process_status_effects()
	
	# Retour au tour du joueur
	turn_count += 1
	current_state = CombatState.PLAYER_TURN
	emit_signal("turn_started", true)

# IA agressive - attaque toujours
func enemy_aggressive_ai(game_state: GameState):
	var jutsu = jutsu_data["basic_attack"]
	execute_jutsu(current_enemy, game_state.player_data, jutsu, game_state)

# IA hit and run - alterne entre attaque et esquive
func enemy_hit_and_run_ai(game_state: GameState):
	if turn_count % 2 == 0:
		# Attaque
		var jutsu = jutsu_data["basic_attack"]
		execute_jutsu(current_enemy, game_state.player_data, jutsu, game_state)
	else:
		# Esquive (pas d'action, mais bonus de défense temporaire)
		apply_status_effect(current_enemy, "dodge", 1)

# IA tactique - utilise des stratégies plus complexes
func enemy_tactical_ai(game_state: GameState):
	var player_hp_ratio = float(game_state.player_data.hp) / float(game_state.player_data.max_hp)
	
	if player_hp_ratio < 0.3:
		# Attaque finale si le joueur est faible
		var jutsu = jutsu_data["basic_attack"]
		jutsu = jutsu.duplicate()
		jutsu.base_damage *= 1.5  # Bonus de dégâts
		execute_jutsu(current_enemy, game_state.player_data, jutsu, game_state)
	else:
		# Attaque normale
		var jutsu = jutsu_data["basic_attack"]
		execute_jutsu(current_enemy, game_state.player_data, jutsu, game_state)

# Traiter les effets de statut
func process_status_effects():
	# Traiter les effets du joueur
	for effect in player_status_effects.keys():
		process_single_status_effect(effect, player_status_effects)
	
	# Traiter les effets de l'ennemi
	for effect in enemy_status_effects.keys():
		process_single_status_effect(effect, enemy_status_effects)

# Traiter un effet de statut spécifique
func process_single_status_effect(effect: String, status_dict: Dictionary):
	match effect:
		"burn":
			# Dégâts de brûlure
			pass
		"confusion":
			# Chance de rater l'attaque
			pass
		"speed_down":
			# Réduction de vitesse
			pass
	
	# Réduire la durée
	status_dict[effect] -= 1
	if status_dict[effect] <= 0:
		status_dict.erase(effect)

# Finir le combat
func end_combat(victory: bool, game_state: GameState):
	current_state = CombatState.COMBAT_END
	
	var rewards = {}
	
	if victory:
		# Récompenses
		rewards["xp"] = current_enemy.get("xp_reward", 0)
		rewards["ryo"] = current_enemy.get("ryo_reward", 0)
		rewards["loot"] = []
		
		# Gagner XP
		game_state.gain_xp(rewards.xp)
		
		# Loot aléatoire
		for loot_item in current_enemy.get("loot", []):
			if randf() < loot_item.chance:
				game_state.add_item(loot_item.id, loot_item.quantity)
				rewards.loot.append(loot_item)
		
		# Statistiques
		game_state.game_stats.battles_won += 1
		game_state.game_stats.enemies_defeated += 1
	
	emit_signal("combat_ended", victory, rewards)

# Obtenir les jutsu disponibles pour le joueur
func get_available_jutsu(game_state: GameState) -> Array:
	var available = []
	
	for jutsu_id in jutsu_data.keys():
		var jutsu = jutsu_data[jutsu_id]
		
		# Vérifier les prérequis de clan
		if jutsu.clan_requirement and jutsu.clan_requirement != game_state.player_data.clan:
			continue
		
		# Vérifier si le joueur a appris ce jutsu
		if jutsu_id != "basic_attack" and jutsu_id not in game_state.learned_jutsu:
			continue
		
		available.append({
			"id": jutsu_id,
			"data": jutsu,
			"can_use": game_state.player_data.chakra >= jutsu.chakra_cost
		})
	
	return available
