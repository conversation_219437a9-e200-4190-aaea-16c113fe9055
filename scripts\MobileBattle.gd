extends Control

# Système de combat tour par tour mobile pour Shinobi Chronicles

enum BattleState {
	STARTING,
	PLAYER_TURN,
	ENEMY_TURN,
	VICTORY,
	DEFEAT
}

var current_state = BattleState.STARTING
var player_data: Dictionary
var enemy_data: Dictionary

# UI Elements
var background: TextureRect
var player_sprite: Sprite2D
var enemy_sprite: Sprite2D
var ui_layer: CanvasLayer

# Combat UI
var player_hp_bar: ProgressBar
var enemy_hp_bar: ProgressBar
var action_buttons: Array = []
var battle_log: RichTextLabel
var turn_indicator: Label

# Combat Data
var player_current_hp: int
var enemy_current_hp: int
var turn_count: int = 1

# Signaux
signal battle_ended(victory: bool, rewards: Dictionary)

func _ready():
	print("⚔️ Système de combat initialisé")
	setup_battle_ui()

func initialize(p_data: Dictionary, e_data: Dictionary):
	self.player_data = p_data
	self.enemy_data = e_data

	# Initialiser les HP
	player_current_hp = player_data.hp
	enemy_current_hp = enemy_data.hp

	# Démarrer le combat
	start_battle()

func setup_battle_ui():
	set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	# Arrière-plan de combat
	create_battle_background()
	
	# Layer UI
	ui_layer = CanvasLayer.new()
	ui_layer.layer = 100
	add_child(ui_layer)
	
	# Sprites des combattants
	create_battle_sprites()
	
	# Interface de combat
	create_combat_interface()

func create_battle_background():
	background = TextureRect.new()
	background.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	
	# Créer un arrière-plan de combat dramatique
	var image = Image.create(720, 1280, false, Image.FORMAT_RGB8)
	
	# Dégradé de combat (rouge sombre vers noir)
	for x in range(720):
		for y in range(1280):
			var progress = float(y) / 1280.0
			var color = Color(0.3, 0.1, 0.1).lerp(Color(0.1, 0.05, 0.05), progress)
			image.set_pixel(x, y, color)
	
	# Ajouter des effets de combat
	add_battle_effects(image)
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	background.texture = texture
	
	add_child(background)

func add_battle_effects(image: Image):
	# Ajouter des éclairs et effets
	for i in range(10):
		var x = randi() % 720
		var y = randi() % 1280
		var effect_size = 20 + randi() % 30

		for dx in range(-effect_size/2, effect_size/2):
			for dy in range(-effect_size/2, effect_size/2):
				var px = x + dx
				var py = y + dy
				if px >= 0 and px < 720 and py >= 0 and py < 1280:
					if randf() < 0.3:
						image.set_pixel(px, py, Color.YELLOW * 0.3)

func create_battle_sprites():
	# Container pour les sprites
	var sprites_container = Node2D.new()
	sprites_container.name = "BattleSprites"
	add_child(sprites_container)
	
	# Sprite du joueur
	create_player_sprite(sprites_container)
	
	# Sprite de l'ennemi
	create_enemy_sprite(sprites_container)

func create_player_sprite(container: Node2D):
	player_sprite = Sprite2D.new()
	player_sprite.position = Vector2(180, 800)  # Côté gauche
	
	# Créer le sprite du ninja joueur
	var image = Image.create(128, 128, false, Image.FORMAT_RGB8)
	image.fill(Color.TRANSPARENT)
	
	# Corps du ninja (bleu pour le joueur)
	for x in range(40, 88):
		for y in range(40, 88):
			image.set_pixel(x, y, Color.BLUE)
	
	# Détails ninja
	add_ninja_details(image, Color.BLUE, Color.WHITE)
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	player_sprite.texture = texture
	
	container.add_child(player_sprite)

func create_enemy_sprite(container: Node2D):
	enemy_sprite = Sprite2D.new()
	enemy_sprite.position = Vector2(540, 600)  # Côté droit, plus haut
	
	# Créer le sprite de l'ennemi
	var image = Image.create(128, 128, false, Image.FORMAT_RGB8)
	image.fill(Color.TRANSPARENT)
	
	# Couleur selon le type d'ennemi
	var enemy_color = get_enemy_color()
	
	# Corps de l'ennemi
	for x in range(40, 88):
		for y in range(40, 88):
			image.set_pixel(x, y, enemy_color)
	
	# Détails ennemis
	add_enemy_details(image, enemy_color)
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	enemy_sprite.texture = texture
	
	container.add_child(enemy_sprite)

func add_ninja_details(image: Image, _body_color: Color, detail_color: Color):
	# Masque ninja
	for x in range(45, 83):
		for y in range(45, 65):
			image.set_pixel(x, y, Color.BLACK)

	# Yeux
	image.set_pixel(55, 55, detail_color)
	image.set_pixel(72, 55, detail_color)

	# Arme
	for y in range(30, 45):
		image.set_pixel(90, y, Color.GRAY)

func add_enemy_details(image: Image, _body_color: Color):
	# Yeux rouges menaçants
	image.set_pixel(55, 55, Color.RED)
	image.set_pixel(72, 55, Color.RED)

	# Griffes ou armes
	for i in range(3):
		for y in range(25, 40):
			image.set_pixel(95 + i * 3, y, Color.DARK_GRAY)

func get_enemy_color() -> Color:
	if not enemy_data:
		return Color.RED
	
	match enemy_data.get("name", ""):
		"Bandit":
			return Color(0.6, 0.3, 0.1)  # Brun
		"Loup Sauvage":
			return Color(0.4, 0.4, 0.4)  # Gris
		"Ninja Renégat":
			return Color(0.5, 0.1, 0.1)  # Rouge foncé
		_:
			return Color.RED

func create_combat_interface():
	# Barres de HP en haut
	create_hp_bars()
	
	# Indicateur de tour
	create_turn_indicator()
	
	# Log de combat
	create_battle_log()
	
	# Boutons d'action
	create_action_buttons()

func create_hp_bars():
	# Container pour les barres HP
	var hp_container = HBoxContainer.new()
	hp_container.set_anchors_and_offsets_preset(Control.PRESET_TOP_WIDE)
	hp_container.size.y = 100
	hp_container.add_theme_constant_override("separation", 50)
	ui_layer.add_child(hp_container)
	
	# Barre HP joueur
	var player_hp_panel = create_hp_panel("🥷 " + player_data.get("name", "Ninja"), Color.BLUE)
	player_hp_bar = player_hp_panel.get_child(1)
	hp_container.add_child(player_hp_panel)
	
	# Barre HP ennemi
	var enemy_hp_panel = create_hp_panel("👹 " + enemy_data.get("name", "Ennemi"), Color.RED)
	enemy_hp_bar = enemy_hp_panel.get_child(1)
	hp_container.add_child(enemy_hp_panel)

func create_hp_panel(character_name: String, color: Color) -> VBoxContainer:
	var panel = VBoxContainer.new()
	panel.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	
	# Nom
	var name_label = Label.new()
	name_label.text = character_name
	name_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	name_label.add_theme_font_size_override("font_size", 18)
	name_label.add_theme_color_override("font_color", color)
	panel.add_child(name_label)
	
	# Barre HP
	var hp_bar = ProgressBar.new()
	hp_bar.custom_minimum_size = Vector2(250, 30)
	hp_bar.add_theme_color_override("fill", color)
	panel.add_child(hp_bar)
	
	# Label HP
	var hp_label = Label.new()
	hp_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	hp_label.add_theme_font_size_override("font_size", 14)
	hp_label.add_theme_color_override("font_color", Color.WHITE)
	panel.add_child(hp_label)
	
	return panel

func create_turn_indicator():
	turn_indicator = Label.new()
	turn_indicator.text = "🎯 TOUR 1 - VOTRE TOUR"
	turn_indicator.set_anchors_and_offsets_preset(Control.PRESET_CENTER_TOP)
	turn_indicator.position.y = 120
	turn_indicator.size = Vector2(400, 40)
	turn_indicator.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	turn_indicator.add_theme_font_size_override("font_size", 20)
	turn_indicator.add_theme_color_override("font_color", Color.YELLOW)
	turn_indicator.add_theme_color_override("font_shadow_color", Color.BLACK)
	ui_layer.add_child(turn_indicator)

func create_battle_log():
	# Container pour le log
	var log_panel = Panel.new()
	log_panel.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	log_panel.size = Vector2(600, 200)
	log_panel.position.y = -50
	log_panel.modulate = Color(0, 0, 0, 0.8)
	ui_layer.add_child(log_panel)
	
	battle_log = RichTextLabel.new()
	battle_log.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	battle_log.bbcode_enabled = true
	battle_log.scroll_following = true
	log_panel.add_child(battle_log)

func create_action_buttons():
	# Container pour les boutons
	var buttons_panel = Panel.new()
	buttons_panel.set_anchors_and_offsets_preset(Control.PRESET_BOTTOM_WIDE)
	buttons_panel.size.y = 200
	buttons_panel.modulate = Color(0, 0, 0, 0.9)
	ui_layer.add_child(buttons_panel)
	
	var grid = GridContainer.new()
	grid.columns = 2
	grid.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	grid.add_theme_constant_override("h_separation", 20)
	grid.add_theme_constant_override("v_separation", 15)
	buttons_panel.add_child(grid)
	
	# Actions de combat
	var actions = [
		{"text": "⚔️ ATTAQUE", "action": "attack", "color": Color.RED},
		{"text": "🛡️ DÉFENSE", "action": "defend", "color": Color.BLUE},
		{"text": "🥷 JUTSU", "action": "jutsu", "color": Color.PURPLE},
		{"text": "🧪 OBJET", "action": "item", "color": Color.GREEN}
	]
	
	for action in actions:
		var btn = create_action_button(action.text, action.action, action.color)
		grid.add_child(btn)
		action_buttons.append(btn)

func create_action_button(text: String, action: String, color: Color) -> Button:
	var button = Button.new()
	button.text = text
	button.custom_minimum_size = Vector2(300, 80)
	button.add_theme_font_size_override("font_size", 18)
	button.add_theme_color_override("font_color", Color.WHITE)
	button.add_theme_color_override("font_hover_color", color)
	button.pressed.connect(_on_action_selected.bind(action))
	
	return button

func start_battle():
	current_state = BattleState.STARTING
	
	# Initialiser les barres HP
	update_hp_bars()
	
	# Message de début
	add_battle_message("[color=yellow]⚔️ LE COMBAT COMMENCE![/color]")
	add_battle_message("🥷 " + player_data.get("name", "Ninja") + " VS 👹 " + enemy_data.get("name", "Ennemi"))
	
	# Animation d'entrée
	animate_battle_start()

func animate_battle_start():
	# Animation des sprites
	if player_sprite:
		player_sprite.position.x = -100
		var tween1 = create_tween()
		tween1.tween_property(player_sprite, "position:x", 180, 1.0)
	
	if enemy_sprite:
		enemy_sprite.position.x = 820
		var tween2 = create_tween()
		tween2.tween_property(enemy_sprite, "position:x", 540, 1.0)
		await tween2.finished
	
	# Commencer le tour du joueur
	current_state = BattleState.PLAYER_TURN
	update_turn_indicator()

func update_hp_bars():
	if player_hp_bar:
		player_hp_bar.max_value = player_data.max_hp
		player_hp_bar.value = player_current_hp
		var hp_panel = player_hp_bar.get_parent()
		if hp_panel.get_child_count() > 2:
			hp_panel.get_child(2).text = str(player_current_hp) + "/" + str(player_data.max_hp)
	
	if enemy_hp_bar:
		enemy_hp_bar.max_value = enemy_data.hp
		enemy_hp_bar.value = enemy_current_hp
		var hp_panel = enemy_hp_bar.get_parent()
		if hp_panel.get_child_count() > 2:
			hp_panel.get_child(2).text = str(enemy_current_hp) + "/" + str(enemy_data.hp)

func update_turn_indicator():
	if not turn_indicator:
		return
	
	match current_state:
		BattleState.PLAYER_TURN:
			turn_indicator.text = "🎯 TOUR " + str(turn_count) + " - VOTRE TOUR"
			turn_indicator.add_theme_color_override("font_color", Color.YELLOW)
		BattleState.ENEMY_TURN:
			turn_indicator.text = "👹 TOUR " + str(turn_count) + " - TOUR ENNEMI"
			turn_indicator.add_theme_color_override("font_color", Color.RED)

func add_battle_message(message: String):
	if battle_log:
		battle_log.append_text(message + "\n")

# Callbacks
func _on_action_selected(action: String):
	if current_state != BattleState.PLAYER_TURN:
		return
	
	print("🎬 Action sélectionnée: ", action)
	
	# Désactiver les boutons
	set_buttons_enabled(false)
	
	# Exécuter l'action
	match action:
		"attack":
			player_attack()
		"defend":
			player_defend()
		"jutsu":
			player_jutsu()
		"item":
			player_use_item()

func player_attack():
	var damage = calculate_damage(player_data.attack, enemy_data.get("defense", 0))
	enemy_current_hp = max(0, enemy_current_hp - damage)
	
	add_battle_message("[color=red]⚔️ " + player_data.name + " attaque pour " + str(damage) + " dégâts![/color]")
	
	# Animation d'attaque
	animate_attack(player_sprite, enemy_sprite)
	
	update_hp_bars()
	
	if enemy_current_hp <= 0:
		end_battle(true)
	else:
		# Tour de l'ennemi
		current_state = BattleState.ENEMY_TURN
		update_turn_indicator()
		await get_tree().create_timer(1.5).timeout
		enemy_turn()

func player_defend():
	add_battle_message("[color=blue]🛡️ " + player_data.name + " se défend! Dégâts réduits au prochain tour.[/color]")
	
	# Tour de l'ennemi avec défense
	current_state = BattleState.ENEMY_TURN
	update_turn_indicator()
	await get_tree().create_timer(1.0).timeout
	enemy_turn(true)

func player_jutsu():
	if player_data.chakra < 5:
		add_battle_message("[color=cyan]💨 Pas assez de chakra pour un jutsu![/color]")
		set_buttons_enabled(true)
		return
	
	var damage = calculate_damage(player_data.attack * 1.5, enemy_data.get("defense", 0))
	enemy_current_hp = max(0, enemy_current_hp - damage)
	
	add_battle_message("[color=purple]🥷 " + player_data.name + " utilise un jutsu! " + str(damage) + " dégâts![/color]")
	
	update_hp_bars()
	
	if enemy_current_hp <= 0:
		end_battle(true)
	else:
		current_state = BattleState.ENEMY_TURN
		update_turn_indicator()
		await get_tree().create_timer(1.5).timeout
		enemy_turn()

func player_use_item():
	add_battle_message("[color=green]🧪 Utilisation d'objet (fonctionnalité à venir)[/color]")
	set_buttons_enabled(true)

func enemy_turn(player_defending: bool = false):
	# IA simple de l'ennemi
	var action = randi() % 3
	
	match action:
		0:  # Attaque
			var damage = calculate_damage(enemy_data.get("attack", 10), player_data.defense)
			if player_defending:
				damage = int(damage / 2.0)
				add_battle_message("[color=blue]🛡️ Dégâts réduits par la défense![/color]")
			
			player_current_hp = max(0, player_current_hp - damage)
			add_battle_message("[color=red]👹 " + enemy_data.name + " attaque pour " + str(damage) + " dégâts![/color]")
			
			animate_attack(enemy_sprite, player_sprite)
		
		1:  # Attaque spéciale
			var damage = calculate_damage(enemy_data.get("attack", 10) * 1.3, player_data.defense)
			if player_defending:
				damage = int(damage / 2.0)
			
			player_current_hp = max(0, player_current_hp - damage)
			add_battle_message("[color=orange]👹 " + enemy_data.name + " utilise une attaque spéciale! " + str(damage) + " dégâts![/color]")
		
		2:  # Défense
			add_battle_message("[color=gray]👹 " + enemy_data.name + " se prépare...[/color]")
	
	update_hp_bars()
	
	if player_current_hp <= 0:
		end_battle(false)
	else:
		# Retour au tour du joueur
		turn_count += 1
		current_state = BattleState.PLAYER_TURN
		update_turn_indicator()
		set_buttons_enabled(true)

func calculate_damage(attack: int, defense: int) -> int:
	var base_damage = attack - defense / 2
	var variance = randi() % 5 - 2  # -2 à +2
	return max(1, base_damage + variance)

func animate_attack(attacker: Sprite2D, target: Sprite2D):
	if not attacker or not target:
		return
	
	var original_pos = attacker.position
	var target_pos = target.position
	var direction = (target_pos - original_pos).normalized() * 50
	
	var tween = create_tween()
	tween.tween_property(attacker, "position", original_pos + direction, 0.2)
	tween.tween_property(attacker, "position", original_pos, 0.2)
	
	# Flash sur la cible
	var flash_tween = create_tween()
	flash_tween.tween_property(target, "modulate", Color.RED, 0.1)
	flash_tween.tween_property(target, "modulate", Color.WHITE, 0.1)

func set_buttons_enabled(enabled: bool):
	for button in action_buttons:
		button.disabled = not enabled

func end_battle(victory: bool):
	if victory:
		current_state = BattleState.VICTORY
		add_battle_message("[color=yellow]🏆 VICTOIRE![/color]")
		
		# Récompenses
		var xp_reward = 30 + enemy_data.get("level", 1) * 10
		var rewards = {"xp": xp_reward, "items": []}
		
		add_battle_message("[color=green]⭐ +" + str(xp_reward) + " XP![/color]")
		
		# Animation de victoire
		if player_sprite:
			var tween = create_tween()
			tween.tween_property(player_sprite, "rotation", PI * 2, 1.0)
		
		await get_tree().create_timer(3.0).timeout
		emit_signal("battle_ended", true, rewards)
	else:
		current_state = BattleState.DEFEAT
		add_battle_message("[color=red]💀 DÉFAITE...[/color]")
		
		# Animation de défaite
		if player_sprite:
			var tween = create_tween()
			tween.tween_property(player_sprite, "modulate", Color.GRAY, 1.0)
		
		await get_tree().create_timer(3.0).timeout
		emit_signal("battle_ended", false, {})
